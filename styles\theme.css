/* Theme Variables */
:root {
  /* Default light theme variables */
  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --accent-color: #3b82f6;
  --card-bg: #ffffff;
  --nav-bg: rgba(248, 250, 252, 0.9);
  --footer-gradient: linear-gradient(45deg, #f1f5f9, #e2e8f0, #f1f5f9, #e2e8f0);
  --button-bg: #3b82f6;
  --button-hover: #2563eb;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --input-bg: #ffffff;
}

/* Light Theme */
.light-mode {
  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --accent-color: #3b82f6;
  --card-bg: #ffffff;
  --nav-bg: rgba(248, 250, 252, 0.9);
  --footer-gradient: linear-gradient(45deg, #f1f5f9, #e2e8f0, #f1f5f9, #e2e8f0);
  --button-bg: #3b82f6;
  --button-hover: #2563eb;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --input-bg: #ffffff;
}

/* Dark Theme */
.dark-mode {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --accent-color: #3b82f6;
  --card-bg: #1e293b;
  --nav-bg: rgba(15, 23, 42, 0.9);
  --footer-gradient: linear-gradient(45deg, #0f172a, #1e3a8a, #0f172a, #1e3a8a);
  --button-bg: #3b82f6;
  --button-hover: #2563eb;
  --border-color: #334155;
  --shadow-color: rgba(0, 0, 0, 0.5);
  --input-bg: #1e293b;
}

/* Base Elements */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Fixed width container */
.max-w-7xl {
  max-width: 1200px !important; /* Fixed width for all content containers */
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Ensure all sections have fixed width content */
section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* Fix for full-width sections with background colors */
section > div:not(.absolute) {
  max-width: 1200px;
  width: 100%;
}

/* Navigation */
nav {
  background-color: var(--nav-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Navigation bar positioning */
nav.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  height: 5rem; /* 80px for h-20 */
}

/* Override hardcoded background colors in navbar */
.light-mode nav.bg-gray-900,
.light-mode nav.bg-opacity-90 {
  background-color: var(--nav-bg) !important;
}

/* Fix for scroll behavior in script.js */
.light-mode nav {
  background-color: rgba(248, 250, 252, 0.7) !important; /* More transparent background */
}

.dark-mode nav {
  background-color: rgba(15, 23, 42, 0.7) !important; /* More transparent background */
}

.light-mode nav.scrolled {
  background-color: rgba(248, 250, 252, 0.8) !important; /* Slightly more opaque when scrolled */
}

.dark-mode nav.scrolled {
  background-color: rgba(15, 23, 42, 0.8) !important; /* Slightly more opaque when scrolled */
}

nav a {
  color: var(--text-primary);
  transition: color 0.3s ease;
}

nav a:hover {
  color: var(--accent-color);
}

/* Cards and Sections */
.bg-gray-800, .bg-dark-blue {
  background-color: var(--card-bg);
  transition: background-color 0.3s ease;
}

.text-white, .text-gray-300 {
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.text-gray-400 {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

/* Buttons */
.bg-blue-600 {
  background-color: var(--button-bg);
  transition: background-color 0.3s ease;
}

.bg-blue-600:hover {
  background-color: var(--button-hover);
}

/* Footer */
footer {
  background-image: var(--footer-gradient) !important;
  transition: background-image 0.3s ease;
}

/* Borders */
.border, .border-t, .border-b, .border-l, .border-r {
  border-color: var(--border-color);
  transition: border-color 0.3s ease;
}

/* Inputs */
input, textarea, select {
  background-color: var(--input-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Shadows */
.shadow, .shadow-md, .shadow-lg, .shadow-xl {
  box-shadow: 0 0 10px 0 var(--shadow-color), 0 4px 6px -1px var(--shadow-color);
  transition: box-shadow 0.3s ease;
}

/* Enhanced shadows for all cards across the site */
.bg-gray-800,
.card,
div[class*="rounded-lg"],
.horizontal-timeline-content .bg-gray-800,
section .bg-gray-800 {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
              0 10px 10px -5px rgba(0, 0, 0, 0.2),
              0 0 15px 0 rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Darker shadows for dark mode */
.dark-mode .bg-gray-800,
.dark-mode .card,
.dark-mode div[class*="rounded-lg"],
.dark-mode .horizontal-timeline-content .bg-gray-800,
.dark-mode section .bg-gray-800 {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5),
              0 10px 10px -5px rgba(0, 0, 0, 0.3),
              0 0 15px 0 rgba(59, 130, 246, 0.3);
}

/* Theme Toggle Button */
#theme-toggle, #mobile-theme-toggle {
  color: var(--text-primary);
  transition: color 0.3s ease;
}

#moon-icon, #mobile-moon-icon {
  color: #f8fafc; /* Light color for moon icon in dark mode */
}

#sun-icon, #mobile-sun-icon {
  color: #f59e0b; /* Amber color for sun icon in light mode */
}

/* Global icon color setting for all icons */
svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.5));
  transition: color 0.3s ease, filter 0.3s ease;
}

/* Logo icon in navigation */
.flex-shrink-0 svg,
nav a.text-2xl.font-bold.text-gradient svg {
  color: #3b82f6 !important; /* Keep blue for logo icon for visibility */
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5)) !important;
}

.dark-mode .flex-shrink-0 svg,
.dark-mode nav a.text-2xl.font-bold.text-gradient svg {
  color: #3b82f6 !important; /* Keep blue for logo icon in dark mode too */
  filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.8)) !important;
}

/* Icon colors for feature icons and other UI elements */
.bg-blue-600.bg-opacity-20 svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.5));
}

/* Process icons with enhanced visibility */
#process .bg-blue-600.rounded-full svg,
.bg-blue-600.rounded-full svg,
.bg-blue-700.rounded-full svg {
  color: #f1f5f9 !important; /* Keep light color for contrast on blue backgrounds */
  filter: drop-shadow(0 0 3px rgba(30, 64, 175, 0.5)) !important;
}

.dark-mode #process .bg-blue-600.rounded-full svg,
.dark-mode .bg-blue-600.rounded-full svg,
.dark-mode .bg-blue-700.rounded-full svg {
  color: #f1f5f9 !important; /* Keep light color in dark mode too for visibility */
  filter: drop-shadow(0 0 3px rgba(30, 64, 175, 0.5)) !important;
}

/* About section icons */
#about .text-blue-500 svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Services section icons */
#services .bg-blue-600.bg-opacity-20 svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Products section icons */
#products .bg-blue-600.bg-opacity-20 svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Why Choose Us section icons */
#why-choose-us .bg-blue-600.bg-opacity-20 svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Product page specific icons */
.product-feature svg,
.product-detail svg,
.product-benefit svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Blog page specific icons */
.blog-post svg,
.blog-category svg,
.blog-author svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Product-specific "Why Choose" section icons */
[id^="product"] .why-choose-section .bg-blue-600.bg-opacity-20 svg,
[class*="product"] .why-choose-section .bg-blue-600.bg-opacity-20 svg,
section:has(h2:contains("Why Choose")) .bg-blue-600.bg-opacity-20 svg {
  color: #1e40af; /* Dark blue */
  filter: drop-shadow(0 0 2px rgba(30, 64, 175, 0.6));
}

/* Dark mode adjustments - make icons dark blue */
.dark-mode svg {
  color: #1e3a8a; /* Dark blue for dark mode */
  filter: drop-shadow(0 0 3px rgba(30, 58, 138, 0.8));
}

/* Exceptions for theme toggle icons */
.dark-mode #moon-icon, .dark-mode #mobile-moon-icon,
#moon-icon, #mobile-moon-icon {
  color: #f8fafc !important; /* Keep light color for moon icon */
  filter: none !important;
}

.dark-mode #sun-icon, .dark-mode #mobile-sun-icon,
#sun-icon, #mobile-sun-icon {
  color: #f59e0b !important; /* Keep amber color for sun icon */
  filter: none !important;
}

/* Preserve gradient text */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #38bdf8, #818cf8);
}

/* Additional fixes for light mode */
.bg-dark-blue {
  background-color: var(--card-bg);
}

/* Fix for product cards */
.bg-gray-800 {
  background-color: var(--card-bg);
}

/* Fix for footer in light mode */
footer.bg-gray-900 {
  background-color: var(--bg-secondary);
}

/* Fix for hero section */
.bg-gray-900 {
  background-color: var(--bg-secondary);
}

/* Fix for testimonials section */
.bg-gray-800 {
  background-color: var(--card-bg);
}

/* Make all section backgrounds consistent in dark mode */
.dark-mode section {
  background-color: var(--bg-primary) !important;
}

/* Ensure sections with explicit bg-gray-900 class maintain consistent background in dark mode */
.dark-mode section.bg-gray-900,
.dark-mode section.py-20.bg-gray-900 {
  background-color: var(--bg-primary) !important;
}

/* Fix for contact form */
.bg-gray-800 input, .bg-gray-800 textarea {
  background-color: var(--input-bg);
  color: var(--text-primary);
}

/* Fix for CTA section in light mode */
.light-mode section.bg-gradient-to-r.from-blue-900.to-gray-900 {
  background: white !important;
  background-image: none !important;
}

.light-mode section.bg-gradient-to-r.from-blue-900.to-gray-900 p.text-gray-300 {
  color: #4b5563 !important; /* gray-700 equivalent */
}

/* Form inputs in light mode */
.light-mode input,
.light-mode textarea,
.light-mode select {
  background-color: white !important;
  color: #1e293b !important; /* text-primary */
  border-color: #e2e8f0 !important; /* border-color */
}

/* Fix for select elements in light mode */
.light-mode select.bg-gray-700,
.light-mode select.dark-select {
  background-color: white !important;
  color: #1e293b !important;
  border-color: #e2e8f0 !important;
}

/* Fix for select dropdown arrow color in light mode */
.light-mode select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%231e293b' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
  background-position: right 0.5rem center !important;
  background-repeat: no-repeat !important;
  background-size: 1.5em 1.5em !important;
  padding-right: 2.5rem !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Footer social media icons - consolidated rules */
footer svg,
footer .flex.space-x-4 a svg,
footer .social-icons svg,
footer a[href*="facebook"] svg,
footer a[href*="twitter"] svg,
footer a[href*="linkedin"] svg,
footer a[href*="instagram"] svg,
footer a[href*="youtube"] svg {
  color: #ffffff; /* White */
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.3));
}

/* Dark mode adjustment for footer social icons - consolidated rules */
.dark-mode footer svg,
.dark-mode footer .flex.space-x-4 a svg,
.dark-mode footer .social-icons svg,
.dark-mode footer a[href*="facebook"] svg,
.dark-mode footer a[href*="twitter"] svg,
.dark-mode footer a[href*="linkedin"] svg,
.dark-mode footer a[href*="instagram"] svg,
.dark-mode footer a[href*="youtube"] svg {
  color: #ffffff; /* White in dark mode too */
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
}

/* Exception for footer logo icon */
footer a.text-2xl.font-bold.text-gradient svg {
  color: #3b82f6 !important; /* Keep blue for footer logo icon */
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5)) !important;
}

.dark-mode footer a.text-2xl.font-bold.text-gradient svg {
  color: #3b82f6 !important; /* Keep blue for footer logo icon in dark mode too */
  filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.8)) !important;
}

/* Make product page hero sections bigger and position lower */
section.pt-32.pb-20.relative {
  padding-top: 50vh; /* Increased top padding to move content down */
  padding-bottom: 10vh; /* Adjust bottom padding */
  min-height: 90vh; /* Ensure minimum height */
  margin-top: 2rem; /* Add margin to push entire section down */
}

/* Adjust hero content positioning */
section.pt-32.pb-20.relative .max-w-7xl {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 1200px !important;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Make hero text larger on product pages */
section.pt-32.pb-20.relative h1 {
  font-size: 3.5rem;
  line-height: 1.2;
}

section.pt-32.pb-20.relative p.text-xl {
  font-size: 1.5rem;
  max-width: 36rem;
  margin-left: auto;
  margin-right: auto;
}

/* Overlay for product hero images - dark mode */
section.pt-32.pb-20.relative .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900 {
  background: rgba(15, 23, 42, 0.4); /* Reduced opacity dark overlay */
  background-image: none !important; /* Ensure no gradient is applied */
}

/* Increase image visibility in product pages */
section.pt-32.pb-20.relative .absolute.inset-0.z-0 img {
  opacity: 0.8; /* Significantly increase opacity from 0.3/0.4 */
}

/* Fix hero image gradient in light mode - reduce white overlay */
.light-mode section.pt-32.pb-20.relative .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900,
.light-mode #home .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900 {
  background: rgba(241, 245, 249, 0.3); /* Reduced opacity light overlay */
  background-image: none !important; /* Ensure no gradient is applied */
}

/* Adjust hero image for light mode - increase visibility */
.light-mode section.pt-32.pb-20.relative .absolute.inset-0.z-0 img,
.light-mode #home .absolute.inset-0.z-0 img {
  opacity: 0.9; /* Increase opacity for better visibility in light mode */
  filter: brightness(0.95) contrast(1.05); /* Subtle image enhancement */
}

/* Ensure text remains visible in light mode */
.light-mode #home h1,
.light-mode #home p {
  color: #1e293b; /* Dark text for light mode */
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5); /* Light text shadow for readability */
}

/* Fix for hero section overlay in light mode */
.light-mode #home .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900.opacity-50 {
  background: linear-gradient(to right, rgba(226, 232, 240, 0.3), rgba(191, 219, 254, 0.3)) !important;
  opacity: 0.4 !important;
}

/* Adjust hero image for light mode */
.light-mode #home .absolute.inset-0.z-0 img {
  opacity: 0.9; /* Increase opacity for better visibility in light mode */
  filter: brightness(1) contrast(1.05); /* Adjust image appearance for light mode */
}

/* Ensure text remains visible in light mode with darker shadow for contrast */
.light-mode #home h1,
.light-mode #home p {
  color: #1e293b; /* Dark text for light mode */
}

/* Fix home hero section width and position */
#home {
  padding-top: 8rem; /* Add top padding to push content down below navbar */
  margin-top: 1rem; /* Add margin to create space between navbar and hero */
}

#home .max-w-7xl {
  max-width: 1200px !important;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Enhanced text shadow for readability in light mode */
.light-mode #home h1,
.light-mode #home p {
  text-shadow: 0 1px 4px rgba(255, 255, 255, 0.8), 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Make View Pricing buttons white in light mode for demo pages */
.light-mode a[href="../pricing.html"],
.light-mode a[href$="pricing.html"],
.light-mode .bg-gray-700.hover\:bg-gray-600 {
  color: white !important;
  background-color: var(--button-bg) !important;
}

.light-mode a[href="../pricing.html"]:hover,
.light-mode a[href$="pricing.html"]:hover,
.light-mode .bg-gray-700.hover\:bg-gray-600:hover {
  background-color: var(--button-hover) !important;
  color: white !important;
}

/* Fix for footer links in light mode */
.light-mode footer a,
.light-mode footer .text-gray-300,
.light-mode footer .light-mode-footer-link {
  color: var(--text-primary);
}

.light-mode footer a:hover,
.light-mode footer .light-mode-footer-link:hover {
  color: var(--accent-color);
}

/* Fix for Order Summary card in checkout page - light mode */
.light-mode .bg-gray-800 {
  background-color: white !important;
  color: var(--text-primary) !important;
}

/* Fix specifically for Terms of Service and Privacy Policy links in Order Summary */
.light-mode .bg-gray-800 .terms-privacy a,
.light-mode .bg-gray-800 a.text-blue-400,
.light-mode .bg-gray-800 a.text-gray-300,
.light-mode .bg-gray-800 a.text-white,
.light-mode .bg-gray-800 .text-sm a {
  color: #3b82f6 !important; /* Blue color for better visibility on white */
  background-color: transparent !important;
}

/* Fix for Terms of Service links in dark mode */
.dark-mode .bg-gray-800 .terms-privacy a,
.dark-mode .bg-gray-800 a.text-blue-400,
.dark-mode .bg-gray-800 a.text-gray-300,
.dark-mode .bg-gray-800 a.text-white,
.dark-mode .bg-gray-800 .text-sm a {
  color: #60a5fa !important; /* Lighter blue for better visibility in dark mode */
  background-color: transparent !important;
}

/* Fix for text colors in Order Summary card in light mode */
.light-mode .bg-gray-800 .text-gray-300,
.light-mode .bg-gray-800 .text-gray-400,
.light-mode .bg-gray-800 .text-white {
  color: #4b5563 !important; /* Gray-600 equivalent for better readability */
}

/* Fix for borders in Order Summary card in light mode */
.light-mode .bg-gray-800 .border-gray-700 {
  border-color: #e5e7eb !important; /* Gray-200 equivalent */
}

/* Particles container */
#particles-container {
  pointer-events: none;
  z-index: 5;
}

#particles-container div {
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: transform;
}

/* Horizontal Timeline Styles */
.horizontal-timeline-container {
  padding: 40px 0 20px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  overflow-x: auto;
  max-width: 1200px; /* Match the fixed width of other containers */
}

.horizontal-timeline-line {
  position: absolute;
  height: 4px;
  background-color: var(--accent-color);
  top: 80px;
  left: 0;
  right: 0;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  z-index: 1;
}

.horizontal-timeline-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  min-width: 100%;
  padding: 0 20px;
}

.horizontal-timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 150px;
  max-width: 200px;
  margin: 0 10px;
}

.horizontal-timeline-marker {
  position: relative;
  z-index: 10;
  margin-bottom: 20px;
}

.horizontal-timeline-dot {
  width: 40px;
  height: 40px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.horizontal-timeline-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.9);
}

.horizontal-timeline-content {
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* Connecting lines from dots to content */
.horizontal-timeline-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 20px;
  background-color: var(--accent-color);
  z-index: 1;
}

/* Enhanced shadow for timeline cards */
.horizontal-timeline-content .bg-gray-800 {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
              0 10px 10px -5px rgba(0, 0, 0, 0.2),
              0 0 15px 0 rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.horizontal-timeline-content .bg-gray-800:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.4),
              0 15px 15px -5px rgba(0, 0, 0, 0.3),
              0 0 20px 0 rgba(59, 130, 246, 0.3);
}

/* Responsive styles for smaller screens */
@media (max-width: 1024px) {
  .horizontal-timeline-items {
    justify-content: flex-start;
    min-width: 900px;
    padding: 0 10px;
  }

  .horizontal-timeline-container {
    overflow-x: auto;
    padding-bottom: 30px;
  }

  .horizontal-timeline-item {
    min-width: 130px;
    margin: 0 5px;
  }
}

@media (max-width: 768px) {
  .horizontal-timeline-items {
    min-width: 700px;
  }

  .horizontal-timeline-dot {
    width: 35px;
    height: 35px;
  }

  .horizontal-timeline-content .bg-gray-800 {
    padding: 10px !important;
  }

  .horizontal-timeline-content .bg-gray-800 h3 {
    font-size: 0.9rem;
  }

  .horizontal-timeline-content .bg-gray-800 p {
    font-size: 0.75rem;
  }
}

/* Remove all text shadows from hero text */
#home h1,
#home p,
section.pt-32.pb-20.relative h1,
section.pt-32.pb-20.relative p,
.hero-title,
.hero-subtitle,
#home .text-4xl,
#home .text-xl,
section.pt-32.pb-20.relative .text-4xl,
section.pt-32.pb-20.relative .text-xl {
  color: #f8fafc !important; /* Light text for both modes */
  text-shadow: none !important; /* Remove all shadows */
}

/* Ensure hero section text remains visible in both modes */
.hero-title,
.hero-subtitle,
#home .text-4xl,
#home .text-xl,
section.pt-32.pb-20.relative .text-4xl,
section.pt-32.pb-20.relative .text-xl {
  color: #f8fafc !important; /* Light text for both modes */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8), 0 2px 6px rgba(0, 0, 0, 0.4);
}

/* Fix for Newsletter Section in article.html for light mode */
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 {
  background: linear-gradient(to right, #e0f2fe, #bfdbfe) !important;
  background-image: linear-gradient(to right, #e0f2fe, #bfdbfe) !important;
}

/* Ensure text in newsletter section is readable in light mode */
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900 h2,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 h2 {
  color: #1e3a8a !important; /* Dark blue text */
}

.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900 p,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 p {
  color: #1e40af !important; /* Slightly lighter blue text */
}

/* Fix hero image gradient in light mode for blog-redesign and article pages */
.light-mode .blog-hero::before,
.light-mode section.article-hero::before {
  background: linear-gradient(to bottom, rgba(241, 245, 249, 0.7), rgba(226, 232, 240, 0.8)) !important;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Ensure blog hero section text remains visible in light mode */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #1e293b !important; /* Dark text for light mode */
  position: relative;
  z-index: 2;
}

/* Adjust blog hero image for light mode */
.light-mode .blog-hero,
.light-mode section.article-hero {
  background-color: #f1f5f9 !important;
}

/* Ensure hero section text remains white in both modes for article page */
.blog-hero h1,
.blog-hero p,
section.article-hero h1,
section.article-hero p {
  color: #f8fafc !important; /* Light text for both modes */
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5); /* Subtle shadow for readability */
}

/* Override the light mode specific rule */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #f8fafc !important; /* Keep text white in light mode too */
}

/* Fix for hero section overlay in light mode */
.light-mode #home .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900.opacity-50 {
  background: linear-gradient(to right, rgba(226, 232, 240, 0.3), rgba(191, 219, 254, 0.3)) !important;
  opacity: 0.4 !important;
}

/* Adjust hero image for light mode */
.light-mode #home .absolute.inset-0.z-0 img {
  opacity: 0.9; /* Increase opacity for better visibility in light mode */
  filter: brightness(1) contrast(1.05); /* Adjust image appearance for light mode */
}

/* Ensure text remains visible in light mode with darker shadow for contrast */
.light-mode #home h1,
.light-mode #home p {
  color: #1e293b; /* Dark text for light mode */
}

/* Fix home hero section width */
#home .max-w-7xl {
  max-width: 1200px !important;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Enhanced text shadow for readability in light mode */
.light-mode #home h1,
.light-mode #home p {
  text-shadow: 0 1px 4px rgba(255, 255, 255, 0.8), 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Make View Pricing buttons white in light mode for demo pages */
.light-mode a[href="../pricing.html"],
.light-mode a[href$="pricing.html"],
.light-mode .bg-gray-700.hover\:bg-gray-600 {
  color: white !important;
  background-color: var(--button-bg) !important;
}

.light-mode a[href="../pricing.html"]:hover,
.light-mode a[href$="pricing.html"]:hover,
.light-mode .bg-gray-700.hover\:bg-gray-600:hover {
  background-color: var(--button-hover) !important;
  color: white !important;
}

/* Fix for footer links in light mode */
.light-mode footer a,
.light-mode footer .text-gray-300,
.light-mode footer .light-mode-footer-link {
  color: var(--text-primary);
}

.light-mode footer a:hover,
.light-mode footer .light-mode-footer-link:hover {
  color: var(--accent-color);
}

/* Fix for Order Summary card in checkout page - light mode */
.light-mode .bg-gray-800 {
  background-color: white !important;
  color: var(--text-primary) !important;
}

/* Fix specifically for Terms of Service and Privacy Policy links in Order Summary */
.light-mode .bg-gray-800 .terms-privacy a,
.light-mode .bg-gray-800 a.text-blue-400,
.light-mode .bg-gray-800 a.text-gray-300,
.light-mode .bg-gray-800 a.text-white,
.light-mode .bg-gray-800 .text-sm a {
  color: #3b82f6 !important; /* Blue color for better visibility on white */
  background-color: transparent !important;
}

/* Fix for Terms of Service links in dark mode */
.dark-mode .bg-gray-800 .terms-privacy a,
.dark-mode .bg-gray-800 a.text-blue-400,
.dark-mode .bg-gray-800 a.text-gray-300,
.dark-mode .bg-gray-800 a.text-white,
.dark-mode .bg-gray-800 .text-sm a {
  color: #60a5fa !important; /* Lighter blue for better visibility in dark mode */
  background-color: transparent !important;
}

/* Fix for text colors in Order Summary card in light mode */
.light-mode .bg-gray-800 .text-gray-300,
.light-mode .bg-gray-800 .text-gray-400,
.light-mode .bg-gray-800 .text-white {
  color: #4b5563 !important; /* Gray-600 equivalent for better readability */
}

/* Fix for borders in Order Summary card in light mode */
.light-mode .bg-gray-800 .border-gray-700 {
  border-color: #e5e7eb !important; /* Gray-200 equivalent */
}

/* Particles container */
#particles-container {
  pointer-events: none;
  z-index: 5;
}

#particles-container div {
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: transform;
}

/* Horizontal Timeline Styles */
.horizontal-timeline-container {
  padding: 40px 0 20px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  overflow-x: auto;
  max-width: 1200px; /* Match the fixed width of other containers */
}

.horizontal-timeline-line {
  position: absolute;
  height: 4px;
  background-color: var(--accent-color);
  top: 80px;
  left: 0;
  right: 0;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  z-index: 1;
}

.horizontal-timeline-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  min-width: 100%;
  padding: 0 20px;
}

.horizontal-timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 150px;
  max-width: 200px;
  margin: 0 10px;
}

.horizontal-timeline-marker {
  position: relative;
  z-index: 10;
  margin-bottom: 20px;
}

.horizontal-timeline-dot {
  width: 40px;
  height: 40px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.horizontal-timeline-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.9);
}

.horizontal-timeline-content {
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* Connecting lines from dots to content */
.horizontal-timeline-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 20px;
  background-color: var(--accent-color);
  z-index: 1;
}

/* Enhanced shadow for timeline cards */
.horizontal-timeline-content .bg-gray-800 {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
              0 10px 10px -5px rgba(0, 0, 0, 0.2),
              0 0 15px 0 rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.horizontal-timeline-content .bg-gray-800:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.4),
              0 15px 15px -5px rgba(0, 0, 0, 0.3),
              0 0 20px 0 rgba(59, 130, 246, 0.3);
}

/* Responsive styles for smaller screens */
@media (max-width: 1024px) {
  .horizontal-timeline-items {
    justify-content: flex-start;
    min-width: 900px;
    padding: 0 10px;
  }

  .horizontal-timeline-container {
    overflow-x: auto;
    padding-bottom: 30px;
  }

  .horizontal-timeline-item {
    min-width: 130px;
    margin: 0 5px;
  }
}

@media (max-width: 768px) {
  .horizontal-timeline-items {
    min-width: 700px;
  }

  .horizontal-timeline-dot {
    width: 35px;
    height: 35px;
  }

  .horizontal-timeline-content .bg-gray-800 {
    padding: 10px !important;
  }

  .horizontal-timeline-content .bg-gray-800 h3 {
    font-size: 0.9rem;
  }

  .horizontal-timeline-content .bg-gray-800 p {
    font-size: 0.75rem;
  }
}

/* Position homepage hero section lower */
#home {
  padding-top: 8rem; /* Add top padding to push content down below navbar */
  margin-top: 1rem; /* Add margin to create space between navbar and hero */
}

/* Remove all text shadows from hero text */
#home h1,
#home p,
section.pt-32.pb-20.relative h1,
section.pt-32.pb-20.relative p,
.hero-title,
.hero-subtitle,
#home .text-4xl,
#home .text-xl,
section.pt-32.pb-20.relative .text-4xl,
section.pt-32.pb-20.relative .text-xl {
  color: #f8fafc !important; /* Light text for both modes */
  text-shadow: none !important; /* Remove all shadows */
}

/* Ensure hero section text remains visible in both modes */
.hero-title,
.hero-subtitle,
#home .text-4xl,
#home .text-xl,
section.pt-32.pb-20.relative .text-4xl,
section.pt-32.pb-20.relative .text-xl {
  color: #f8fafc !important; /* Light text for both modes */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8), 0 2px 6px rgba(0, 0, 0, 0.4);
}

/* Additional positioning for product page hero sections */
section.pt-32.pb-20.relative {
  padding-top: 50vh; /* Increased top padding to move content down */
  padding-bottom: 10vh; /* Adjust bottom padding */
  min-height: 90vh; /* Ensure minimum height */
  margin-top: 2rem; /* Add margin to push entire section down */
}

/* Fix for Newsletter Section in article.html for light mode */
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 {
  background: linear-gradient(to right, #e0f2fe, #bfdbfe) !important;
  background-image: linear-gradient(to right, #e0f2fe, #bfdbfe) !important;
}

/* Ensure text in newsletter section is readable in light mode */
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900 h2,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 h2 {
  color: #1e3a8a !important; /* Dark blue text */
}

.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900 p,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 p {
  color: #1e40af !important; /* Slightly lighter blue text */
}

/* Fix hero image gradient in light mode for blog-redesign and article pages */
.light-mode .blog-hero::before,
.light-mode section.article-hero::before {
  background: linear-gradient(to bottom, rgba(241, 245, 249, 0.7), rgba(226, 232, 240, 0.8)) !important;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Ensure blog hero section text remains visible in light mode */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #1e293b !important; /* Dark text for light mode */
  position: relative;
  z-index: 2;
}

/* Adjust blog hero image for light mode */
.light-mode .blog-hero,
.light-mode section.article-hero {
  background-color: #f1f5f9 !important;
}

/* Force white text for article titles in both modes */
html.light-mode section.article-hero h1.text-4xl,
html.dark-mode section.article-hero h1.text-4xl,
section.article-hero h1.text-4xl,
html.light-mode section.article-hero .article-title,
html.dark-mode section.article-hero .article-title,
section.article-hero .article-title {
  color: #ffffff !important; /* Pure white text */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important; /* Shadow for readability */
  position: relative !important;
  z-index: 20 !important; /* Very high z-index to ensure visibility */
}

/* Additional selector for article title with inline styles */
[data-article-title],
.article-hero-title,
#articleTitle {
  color: #ffffff !important; /* Pure white text */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
}

/* Fix hero image gradient in light mode for blog-redesign and article pages */
.light-mode .blog-hero::before,
.light-mode section.article-hero::before {
  background: linear-gradient(to bottom, rgba(241, 245, 249, 0.7), rgba(226, 232, 240, 0.8)) !important;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Ensure blog hero section text remains visible in light mode */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #1e293b !important; /* Dark text for light mode */
  position: relative;
  z-index: 2;
}

/* Adjust blog hero image for light mode */
.light-mode .blog-hero,
.light-mode section.article-hero {
  background-color: #f1f5f9 !important;
}

/* Ensure hero section text remains white in both modes for article page */
.blog-hero h1,
.blog-hero p,
section.article-hero h1,
section.article-hero p {
  color: #f8fafc !important; /* Light text for both modes */
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5); /* Subtle shadow for readability */
}

/* Override the light mode specific rule */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #f8fafc !important; /* Keep text white in light mode too */
}

/* Fix for hero section overlay in light mode */
.light-mode #home .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900.opacity-50 {
  background: linear-gradient(to right, rgba(226, 232, 240, 0.3), rgba(191, 219, 254, 0.3)) !important;
  opacity: 0.4 !important;
}

/* Adjust hero image for light mode */
.light-mode #home .absolute.inset-0.z-0 img {
  opacity: 0.9; /* Increase opacity for better visibility in light mode */
  filter: brightness(1) contrast(1.05); /* Adjust image appearance for light mode */
}

/* Ensure text remains visible in light mode with darker shadow for contrast */
.light-mode #home h1,
.light-mode #home p {
  color: #1e293b; /* Dark text for light mode */
}

/* Fix home hero section width and position */
#home {
  padding-top: 8rem; /* Add top padding to push content down below navbar */
  margin-top: 1rem; /* Add margin to create space between navbar and hero */
}

#home .max-w-7xl {
  max-width: 1200px !important;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Enhanced text shadow for readability in light mode */
.light-mode #home h1,
.light-mode #home p {
  text-shadow: 0 1px 4px rgba(255, 255, 255, 0.8), 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Make View Pricing buttons white in light mode for demo pages */
.light-mode a[href="../pricing.html"],
.light-mode a[href$="pricing.html"],
.light-mode .bg-gray-700.hover\:bg-gray-600 {
  color: white !important;
  background-color: var(--button-bg) !important;
}

.light-mode a[href="../pricing.html"]:hover,
.light-mode a[href$="pricing.html"]:hover,
.light-mode .bg-gray-700.hover\:bg-gray-600:hover {
  background-color: var(--button-hover) !important;
  color: white !important;
}

/* Fix for footer links in light mode */
.light-mode footer a,
.light-mode footer .text-gray-300,
.light-mode footer .light-mode-footer-link {
  color: var(--text-primary);
}

.light-mode footer a:hover,
.light-mode footer .light-mode-footer-link:hover {
  color: var(--accent-color);
}

/* Fix for Order Summary card in checkout page - light mode */
.light-mode .bg-gray-800 {
  background-color: white !important;
  color: var(--text-primary) !important;
}

/* Fix specifically for Terms of Service and Privacy Policy links in Order Summary */
.light-mode .bg-gray-800 .terms-privacy a,
.light-mode .bg-gray-800 a.text-blue-400,
.light-mode .bg-gray-800 a.text-gray-300,
.light-mode .bg-gray-800 a.text-white,
.light-mode .bg-gray-800 .text-sm a {
  color: #3b82f6 !important; /* Blue color for better visibility on white */
  background-color: transparent !important;
}

/* Fix for Terms of Service links in dark mode */
.dark-mode .bg-gray-800 .terms-privacy a,
.dark-mode .bg-gray-800 a.text-blue-400,
.dark-mode .bg-gray-800 a.text-gray-300,
.dark-mode .bg-gray-800 a.text-white,
.dark-mode .bg-gray-800 .text-sm a {
  color: #60a5fa !important; /* Lighter blue for better visibility in dark mode */
  background-color: transparent !important;
}

/* Fix for text colors in Order Summary card in light mode */
.light-mode .bg-gray-800 .text-gray-300,
.light-mode .bg-gray-800 .text-gray-400,
.light-mode .bg-gray-800 .text-white {
  color: #4b5563 !important; /* Gray-600 equivalent for better readability */
}

/* Fix for borders in Order Summary card in light mode */
.light-mode .bg-gray-800 .border-gray-700 {
  border-color: #e5e7eb !important; /* Gray-200 equivalent */
}

/* Particles container */
#particles-container {
  pointer-events: none;
  z-index: 5;
}

#particles-container div {
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: transform;
}

/* Horizontal Timeline Styles */
.horizontal-timeline-container {
  padding: 40px 0 20px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  overflow-x: auto;
  max-width: 1200px; /* Match the fixed width of other containers */
}

.horizontal-timeline-line {
  position: absolute;
  height: 4px;
  background-color: var(--accent-color);
  top: 80px;
  left: 0;
  right: 0;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  z-index: 1;
}

.horizontal-timeline-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  min-width: 100%;
  padding: 0 20px;
}

.horizontal-timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 150px;
  max-width: 200px;
  margin: 0 10px;
}

.horizontal-timeline-marker {
  position: relative;
  z-index: 10;
  margin-bottom: 20px;
}

.horizontal-timeline-dot {
  width: 40px;
  height: 40px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.horizontal-timeline-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.9);
}

.horizontal-timeline-content {
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* Connecting lines from dots to content */
.horizontal-timeline-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 20px;
  background-color: var(--accent-color);
  z-index: 1;
}

/* Enhanced shadow for timeline cards */
.horizontal-timeline-content .bg-gray-800 {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
              0 10px 10px -5px rgba(0, 0, 0, 0.2),
              0 0 15px 0 rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.horizontal-timeline-content .bg-gray-800:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.4),
              0 15px 15px -5px rgba(0, 0, 0, 0.3),
              0 0 20px 0 rgba(59, 130, 246, 0.3);
}

/* Responsive styles for smaller screens */
@media (max-width: 1024px) {
  .horizontal-timeline-items {
    justify-content: flex-start;
    min-width: 900px;
    padding: 0 10px;
  }

  .horizontal-timeline-container {
    overflow-x: auto;
    padding-bottom: 30px;
  }

  .horizontal-timeline-item {
    min-width: 130px;
    margin: 0 5px;
  }
}

@media (max-width: 768px) {
  .horizontal-timeline-items {
    min-width: 700px;
  }

  .horizontal-timeline-dot {
    width: 35px;
    height: 35px;
  }

  .horizontal-timeline-content .bg-gray-800 {
    padding: 10px !important;
  }

  .horizontal-timeline-content .bg-gray-800 h3 {
    font-size: 0.9rem;
  }

  .horizontal-timeline-content .bg-gray-800 p {
    font-size: 0.75rem;
  }
}

/* Position homepage hero section lower */
#home {
  padding-top: 8rem; /* Add top padding to push content down below navbar */
  margin-top: 1rem; /* Add margin to create space between navbar and hero */
}

/* Remove all text shadows from hero text */
#home h1,
#home p,
section.pt-32.pb-20.relative h1,
section.pt-32.pb-20.relative p,
.hero-title,
.hero-subtitle,
#home .text-4xl,
#home .text-xl,
section.pt-32.pb-20.relative .text-4xl,
section.pt-32.pb-20.relative .text-xl {
  color: #f8fafc !important; /* Light text for both modes */
  text-shadow: none !important; /* Remove all shadows */
}

/* Ensure hero section text remains visible in both modes */
.hero-title,
.hero-subtitle,
#home .text-4xl,
#home .text-xl,
section.pt-32.pb-20.relative .text-4xl,
section.pt-32.pb-20.relative .text-xl {
  color: #f8fafc !important; /* Light text for both modes */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8), 0 2px 6px rgba(0, 0, 0, 0.4);
}

/* Additional positioning for product page hero sections */
section.pt-32.pb-20.relative {
  padding-top: 50vh; /* Increased top padding to move content down */
  padding-bottom: 10vh; /* Adjust bottom padding */
  min-height: 90vh; /* Ensure minimum height */
  margin-top: 2rem; /* Add margin to push entire section down */
}

/* Fix for Newsletter Section in article.html for light mode */
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 {
  background: linear-gradient(to right, #e0f2fe, #bfdbfe) !important;
  background-image: linear-gradient(to right, #e0f2fe, #bfdbfe) !important;
}

/* Ensure text in newsletter section is readable in light mode */
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900 h2,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 h2 {
  color: #1e3a8a !important; /* Dark blue text */
}

.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-indigo-900 p,
.light-mode section.py-16.bg-gradient-to-r.from-blue-900.to-gray-900 p {
  color: #1e40af !important; /* Slightly lighter blue text */
}

/* Fix hero image gradient in light mode for blog-redesign and article pages */
.light-mode .blog-hero::before,
.light-mode section.article-hero::before {
  background: linear-gradient(to bottom, rgba(241, 245, 249, 0.7), rgba(226, 232, 240, 0.8)) !important;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Ensure blog hero section text remains visible in light mode */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #1e293b !important; /* Dark text for light mode */
  position: relative;
  z-index: 2;
}

/* Adjust blog hero image for light mode */
.light-mode .blog-hero,
.light-mode section.article-hero {
  background-color: #f1f5f9 !important;
}

/* Ensure hero section text remains white in both modes for article page */
.blog-hero h1,
.blog-hero p,
section.article-hero h1,
section.article-hero p {
  color: #f8fafc !important; /* Light text for both modes */
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5); /* Subtle shadow for readability */
}

/* Override the light mode specific rule */
.light-mode .blog-hero h1,
.light-mode .blog-hero p,
.light-mode section.article-hero h1,
.light-mode section.article-hero p {
  color: #f8fafc !important; /* Keep text white in light mode too */
}

/* Fix for hero section overlay in light mode */
.light-mode #home .absolute.inset-0.bg-gradient-to-r.from-gray-900.to-blue-900.opacity-50 {
  background: linear-gradient(to right, rgba(226, 232, 240, 0.3), rgba(191, 219, 254, 0.3)) !important;
  opacity: 0.4 !important;
}

/* Adjust hero image for light mode */
.light-mode #home .absolute.inset-0.z-0 img {
  opacity: 0.9; /* Increase opacity for better visibility in light mode */
  filter: brightness(1) contrast(1.05); /* Adjust image appearance for light mode */
}

/* Ensure text remains visible in light mode with darker shadow for contrast */
.light-mode #home h1,
.light-mode #home p {
  color: #1e293b; /* Dark text for light mode */
}

/* Fix home hero section width and position */
#home {
  padding-top: 8rem; /* Add top padding to push content down below navbar */
  margin-top: 1rem; /* Add margin to create space between navbar and hero */
}

#home .max-w-7xl {
  max-width: 1200px !important;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Enhanced text shadow for readability in light mode */
.light-mode #home h1,
.light-mode #home p {
  text-shadow: 0 1px 4px rgba(255, 255, 255, 0.8), 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Make View Pricing buttons white in light mode for demo pages */
.light-mode a[href="../pricing.html"],
.light-mode a[href$="pricing.html"],
.light-mode .bg-gray-700.hover\:bg-gray-600 {
  color: white !important;
  background-color: var(--button-bg) !important;
}

.light-mode a[href="../pricing.html"]:hover,
.light-mode a[href$="pricing.html"]:hover,
.light-mode .bg-gray-700.hover\:bg-gray-600:hover {
  background-color: var(--button-hover) !important;
  color: white !important;
}

/* Fix for footer links in light mode */
.light-mode footer a,
.light-mode footer .text-gray-300,
.light-mode footer .light-mode-footer-link {
  color: var(--text-primary);
}

.light-mode footer a:hover,
.light-mode footer .light-mode-footer-link:hover {
  color: var(--accent-color);
}

/* Fix for Order Summary card in checkout page - light mode */
.light-mode .bg-gray-800 {
  background-color: white !important;
  color: var(--text-primary) !important;
}

/* Fix specifically for Terms of Service and Privacy Policy links in Order Summary */
.light-mode .bg-gray-800 .terms-privacy a,
.light-mode .bg-gray-800 a.text-blue-400,
.light-mode .bg-gray-800 a.text-gray-300,
.light-mode .bg-gray-800 a.text-white,
.light-mode .bg-gray-800 .text-sm a {
  color: #3b82f6 !important; /* Blue color for better visibility on white */
  background-color: transparent !important;
}

/* Fix for Terms of Service links in dark mode */
.dark-mode .bg-gray-800 .terms-privacy a,
.dark-mode .bg-gray-800 a.text-blue-400,
.dark-mode .bg-gray-800 a.text-gray-300,
.dark-mode .bg-gray-800 a.text-white,
.dark-mode .bg-gray-800 .text-sm a {
  color: #60a5fa !important; /* Lighter blue for better visibility in dark mode */
  background-color: transparent !important;
}

/* Fix for text colors in Order Summary card in light mode */
.light-mode .bg-gray-800 .text-gray-300,
.light-mode .bg-gray-800 .text-gray-400,
.light-mode .bg-gray-800 .text-white {
  color: #4b5563 !important; /* Gray-600 equivalent for better readability */
}

/* Fix for borders in Order Summary card in light mode */
.light-mode .bg-gray-800 .border-gray-700 {
  border-color: #e5e7eb !important; /* Gray-200 equivalent */
}

/* Particles container */
#particles-container {
  pointer-events: none;
  z-index: 5;
}

#particles-container div {
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: transform;
}

/* Horizontal Timeline Styles */
.horizontal-timeline-container {
  padding: 40px 0 20px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  overflow-x: auto;
  max-width: 1200px; /* Match the fixed width of other containers */
}

.horizontal-timeline-line {
  position: absolute;
  height: 4px;
  background-color: var(--accent-color);
  top: 80px;
  left: 0;
  right: 0;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  z-index: 1;
}

.horizontal-timeline-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  min-width: 100%;
  padding: 0 20px;
}

.horizontal-timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 150px;
  max-width: 200px;
  margin: 0 10px;
}

.horizontal-timeline-marker {
  position: relative;
  z-index: 10;
  margin-bottom: 20px;
}

.horizontal-timeline-dot {
  width: 40px;
  height: 40px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.horizontal-timeline-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.9);
}

.horizontal-timeline-content {
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* Connecting lines from dots to content */
.horizontal-timeline-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 20px;
  background-color: var(--accent-color);
  z-index: 1;
}

/* Enhanced shadow for timeline cards */
.horizontal-timeline-content .bg-gray-800 {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
              0 10px 10px -5px rgba(0, 0, 0, 0.2),
              0 0 15px 0 rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.horizontal-timeline-content .bg-gray-800:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.4),
              0 15px 15px -5px rgba(0, 0, 0, 0.3),
              0 0 20px 0 rgba(59, 130, 246, 0.3);
}

/* Responsive styles for smaller screens */
@media (max-width: 1024px) {
  .horizontal-timeline-items {
    justify-content: flex-start;
    min-width: 900px;
    padding: 0 10px;
  }



