/**
 * CureOx User Preferences System
 * Tracks user preferences and behavior to personalize content
 */

class UserPreferenceTracker {
  constructor() {
    this.preferences = this.loadPreferences();
    this.initializeTracking();
  }

  /**
   * Load saved preferences from localStorage
   */
  loadPreferences() {
    const savedPreferences = localStorage.getItem('cureox_user_preferences');
    return savedPreferences ? JSON.parse(savedPreferences) : {
      industry: null,
      role: null,
      interests: [],
      visitedPages: [],
      lastVisit: null,
      visitCount: 0,
      preferredProduct: null,
      blogCategories: [],
      darkMode: true
    };
  }

  /**
   * Save preferences to localStorage
   */
  savePreferences() {
    localStorage.setItem('cureox_user_preferences', JSON.stringify(this.preferences));
  }

  /**
   * Initialize tracking of user behavior
   */
  initializeTracking() {
    // Track page visits
    this.trackPageVisit();

    // Track product interest
    this.trackProductInterest();

    // Track blog category interest
    this.trackBlogInterest();

    // Track form interactions
    this.trackFormInteractions();

    // Update visit count and timestamp
    this.preferences.visitCount++;
    this.preferences.lastVisit = new Date().toISOString();
    this.savePreferences();
  }

  /**
   * Track which pages the user visits
   */
  trackPageVisit() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    
    // Add to visited pages if not already there
    if (!this.preferences.visitedPages.includes(currentPage)) {
      this.preferences.visitedPages.push(currentPage);
    }

    // Determine product interest based on page visits
    if (currentPage === 'product1.html' || currentPage.includes('dentalpro')) {
      this.incrementProductInterest('DentalPro');
    } else if (currentPage === 'product2.html' || currentPage.includes('pharmtrack')) {
      this.incrementProductInterest('PharmTrack');
    } else if (currentPage === 'product3.html' || currentPage.includes('medoffice')) {
      this.incrementProductInterest('MedOffice');
    }

    this.savePreferences();
  }

  /**
   * Track which products the user is interested in
   */
  trackProductInterest() {
    // Track clicks on product links and buttons
    document.querySelectorAll('a[href*="product"], button[data-product]').forEach(element => {
      element.addEventListener('click', (e) => {
        const productLink = e.currentTarget.getAttribute('href') || '';
        const productData = e.currentTarget.getAttribute('data-product');
        
        if (productLink.includes('product1') || productData === 'dentalpro') {
          this.incrementProductInterest('DentalPro');
        } else if (productLink.includes('product2') || productData === 'pharmtrack') {
          this.incrementProductInterest('PharmTrack');
        } else if (productLink.includes('product3') || productData === 'medoffice') {
          this.incrementProductInterest('MedOffice');
        }
        
        this.savePreferences();
      });
    });
  }

  /**
   * Increment interest count for a specific product
   */
  incrementProductInterest(product) {
    // Set as preferred product if it's the first one or has more interest
    if (!this.preferences.preferredProduct) {
      this.preferences.preferredProduct = product;
    } else {
      // Simple algorithm: most recently viewed product becomes preferred
      this.preferences.preferredProduct = product;
    }
  }

  /**
   * Track blog category interests
   */
  trackBlogInterest() {
    // Track clicks on blog category links
    document.querySelectorAll('.blog-category, [data-category]').forEach(element => {
      element.addEventListener('click', (e) => {
        const category = e.currentTarget.getAttribute('data-category') || 
                         e.currentTarget.textContent.trim();
        
        if (category && !this.preferences.blogCategories.includes(category)) {
          this.preferences.blogCategories.push(category);
          this.savePreferences();
        }
      });
    });

    // Track time spent on blog articles
    if (window.location.pathname.includes('blog')) {
      let startTime = Date.now();
      let articleCategory = document.querySelector('[data-article-category]')?.getAttribute('data-article-category');
      
      if (articleCategory) {
        // Track engagement when user leaves page or after significant time
        window.addEventListener('beforeunload', () => {
          const timeSpent = (Date.now() - startTime) / 1000; // in seconds
          
          // If user spent more than 30 seconds, consider it meaningful engagement
          if (timeSpent > 30 && !this.preferences.blogCategories.includes(articleCategory)) {
            this.preferences.blogCategories.push(articleCategory);
            this.savePreferences();
          }
        });
      }
    }
  }

  /**
   * Track form interactions to determine user role and industry
   */
  trackFormInteractions() {
    // Track role and industry selections in forms
    const industrySelect = document.querySelector('select[name="industry"]');
    const roleSelect = document.querySelector('select[name="role"]');
    
    if (industrySelect) {
      industrySelect.addEventListener('change', (e) => {
        this.preferences.industry = e.target.value;
        this.savePreferences();
        this.applyPersonalization();
      });
    }
    
    if (roleSelect) {
      roleSelect.addEventListener('change', (e) => {
        this.preferences.role = e.target.value;
        this.savePreferences();
        this.applyPersonalization();
      });
    }

    // Track form submissions
    document.querySelectorAll('form').forEach(form => {
      form.addEventListener('submit', (e) => {
        // Get industry and role from form if available
        const industryField = form.querySelector('[name="industry"]');
        const roleField = form.querySelector('[name="role"]');
        
        if (industryField && industryField.value) {
          this.preferences.industry = industryField.value;
        }
        
        if (roleField && roleField.value) {
          this.preferences.role = roleField.value;
        }
        
        this.savePreferences();
        this.applyPersonalization();
      });
    });
  }

  /**
   * Apply personalization based on user preferences
   */
  applyPersonalization() {
    // Personalize content based on preferences
    this.personalizeHero();
    this.personalizeProducts();
    this.personalizeCallToAction();
    this.personalizeBlogContent();
  }

  /**
   * Personalize hero section based on user preferences
   */
  personalizeHero() {
    const heroTitle = document.querySelector('.hero-title');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    
    if (heroTitle && this.preferences.industry) {
      // Customize hero message based on industry
      const industryMessages = {
        'dental': 'Transform Your Dental Practice with CureOx',
        'pharmacy': 'Streamline Your Pharmacy Operations with CureOx',
        'medical': 'Elevate Your Medical Practice with CureOx',
        'hospital': 'Enhance Hospital Management with CureOx'
      };
      
      if (industryMessages[this.preferences.industry]) {
        heroTitle.textContent = industryMessages[this.preferences.industry];
      }
    }
    
    if (heroSubtitle && this.preferences.role) {
      // Customize subtitle based on role
      const roleMessages = {
        'administrator': 'Designed for healthcare administrators seeking operational excellence',
        'clinician': 'Built for clinicians focused on patient care',
        'technician': 'Optimized for technical staff managing healthcare systems',
        'owner': 'Perfect for practice owners looking to maximize efficiency and growth'
      };
      
      if (roleMessages[this.preferences.role]) {
        heroSubtitle.textContent = roleMessages[this.preferences.role];
      }
    }
  }

  /**
   * Personalize product recommendations based on user preferences
   */
  personalizeProducts() {
    // If user has a preferred product, highlight it
    if (this.preferences.preferredProduct) {
      const productCards = document.querySelectorAll('.product-card');
      
      productCards.forEach(card => {
        const productName = card.getAttribute('data-product');
        
        if (productName === this.preferences.preferredProduct) {
          // Highlight the preferred product
          card.classList.add('preferred-product');
          card.style.transform = 'scale(1.05)';
          card.style.boxShadow = '0 10px 25px -5px rgba(59, 130, 246, 0.5)';
          
          // Move preferred product to the beginning if in a grid
          const parentGrid = card.parentElement;
          if (parentGrid && parentGrid.children.length > 1) {
            parentGrid.insertBefore(card, parentGrid.firstChild);
          }
        }
      });
    }
  }

  /**
   * Personalize call-to-action buttons based on user preferences
   */
  personalizeCallToAction() {
    const ctaButtons = document.querySelectorAll('.cta-button');
    
    ctaButtons.forEach(button => {
      // If user has visited multiple times, change CTA text
      if (this.preferences.visitCount > 3) {
        if (button.textContent.includes('Learn More')) {
          button.textContent = 'See Pricing';
        } else if (button.textContent.includes('Explore')) {
          button.textContent = 'Request Demo';
        }
      }
      
      // If user has a preferred product, customize CTA
      if (this.preferences.preferredProduct) {
        const demoLink = button.getAttribute('href');
        
        if (demoLink && demoLink.includes('demo.html')) {
          // Add preferred product as parameter
          button.setAttribute('href', `demo.html?product=${this.preferences.preferredProduct.toLowerCase()}`);
        }
      }
    });
  }

  /**
   * Personalize blog content based on user interests
   */
  personalizeBlogContent() {
    // If on blog page and user has category preferences
    if (window.location.pathname.includes('blog') && this.preferences.blogCategories.length > 0) {
      const blogPosts = document.querySelectorAll('.blog-post');
      const featuredPost = document.querySelector('.featured-post');
      
      // Find posts matching user interests to feature
      if (featuredPost && blogPosts.length > 0) {
        let matchingPost = null;
        
        // Look for a post matching user interests
        blogPosts.forEach(post => {
          const postCategory = post.getAttribute('data-category');
          
          if (postCategory && this.preferences.blogCategories.includes(postCategory)) {
            matchingPost = post;
          }
        });
        
        // If found a matching post, swap it with the featured post
        if (matchingPost) {
          const featuredContent = featuredPost.innerHTML;
          const matchingContent = matchingPost.innerHTML;
          
          featuredPost.innerHTML = matchingContent;
          matchingPost.innerHTML = featuredContent;
          
          // Add a personalized badge
          const badge = document.createElement('div');
          badge.className = 'bg-blue-600 text-white text-xs px-2 py-1 rounded absolute top-4 right-4';
          badge.textContent = 'Recommended for you';
          featuredPost.style.position = 'relative';
          featuredPost.appendChild(badge);
        }
      }
    }
  }

  /**
   * Get user's preferred product
   */
  getPreferredProduct() {
    return this.preferences.preferredProduct;
  }

  /**
   * Get user's industry
   */
  getIndustry() {
    return this.preferences.industry;
  }

  /**
   * Get user's role
   */
  getRole() {
    return this.preferences.role;
  }

  /**
   * Get user's blog category interests
   */
  getBlogInterests() {
    return this.preferences.blogCategories;
  }

  /**
   * Set user's industry manually
   */
  setIndustry(industry) {
    this.preferences.industry = industry;
    this.savePreferences();
    this.applyPersonalization();
  }

  /**
   * Set user's role manually
   */
  setRole(role) {
    this.preferences.role = role;
    this.savePreferences();
    this.applyPersonalization();
  }

  /**
   * Clear all user preferences
   */
  clearPreferences() {
    localStorage.removeItem('cureox_user_preferences');
    this.preferences = {
      industry: null,
      role: null,
      interests: [],
      visitedPages: [],
      lastVisit: null,
      visitCount: 0,
      preferredProduct: null,
      blogCategories: [],
      darkMode: true
    };
  }
}

// Initialize the user preference tracker when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.userPreferences = new UserPreferenceTracker();
});
