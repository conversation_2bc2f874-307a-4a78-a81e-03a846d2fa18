document.addEventListener('DOMContentLoaded', function() {
  // Initialize AOS with custom settings
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: false,
    mirror: true
  });

  // Create particles for hero section
  const particlesContainer = document.getElementById('particles-container');
  if (particlesContainer) {
    // Create particles
    for (let i = 0; i < 50; i++) {
      createParticle(particlesContainer);
    }
  }

  // Function to create a single particle
  function createParticle(container) {
    const particle = document.createElement('div');

    // Set particle style
    particle.style.position = 'absolute';
    particle.style.width = Math.random() * 5 + 2 + 'px';
    particle.style.height = particle.style.width;
    particle.style.backgroundColor = 'rgba(255, 255, 255, ' + (Math.random() * 0.3 + 0.1) + ')';
    particle.style.borderRadius = '50%';

    // Set initial position
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';

    // Add to container
    container.appendChild(particle);

    // Animate the particle
    animateParticle(particle);
  }

  // Function to animate a particle
  function animateParticle(particle) {
    // Random movement parameters - only x-axis movement
    const duration = Math.random() * 30000 + 20000; // 20-50 seconds
    const xMovement = Math.random() * 40 - 20; // -20 to 20 vw

    // Set transition
    particle.style.transition = 'transform ' + duration + 'ms linear';

    // Apply transform - only x movement
    particle.style.transform = 'translateX(' + xMovement + 'vw)';

    // When animation completes, reset and animate again
    setTimeout(() => {
      // Reset position without transition
      particle.style.transition = 'none';
      particle.style.transform = 'translateX(0)';

      // Force reflow
      particle.offsetHeight;

      // Animate again
      setTimeout(() => {
        animateParticle(particle);
      }, 50);
    }, duration);
  }

  // Theme toggle functionality
  const themeToggle = document.getElementById('theme-toggle');
  const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
  const moonIcon = document.getElementById('moon-icon');
  const sunIcon = document.getElementById('sun-icon');
  const mobileMoonIcon = document.getElementById('mobile-moon-icon');
  const mobileSunIcon = document.getElementById('mobile-sun-icon');

  // Check for saved theme preference or use dark mode by default
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'light') {
    document.documentElement.classList.add('light-mode');
    document.documentElement.classList.remove('dark-mode');
    if (moonIcon && sunIcon) {
      moonIcon.classList.add('hidden');
      sunIcon.classList.remove('hidden');
    }
    if (mobileMoonIcon && mobileSunIcon) {
      mobileMoonIcon.classList.add('hidden');
      mobileSunIcon.classList.remove('hidden');
    }
  } else {
    document.documentElement.classList.add('dark-mode');
    document.documentElement.classList.remove('light-mode');
    if (moonIcon && sunIcon) {
      moonIcon.classList.remove('hidden');
      sunIcon.classList.add('hidden');
    }
    if (mobileMoonIcon && mobileSunIcon) {
      mobileMoonIcon.classList.remove('hidden');
      mobileSunIcon.classList.add('hidden');
    }
    // Only set default if no preference exists
    if (!savedTheme) {
      localStorage.setItem('theme', 'dark');
    }
  }

  // Toggle theme function
  function toggleTheme() {
    if (document.documentElement.classList.contains('light-mode')) {
      document.documentElement.classList.remove('light-mode');
      document.documentElement.classList.add('dark-mode');
      if (moonIcon && sunIcon) {
        moonIcon.classList.remove('hidden');
        sunIcon.classList.add('hidden');
      }
      if (mobileMoonIcon && mobileSunIcon) {
        mobileMoonIcon.classList.remove('hidden');
        mobileSunIcon.classList.add('hidden');
      }
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark-mode');
      document.documentElement.classList.add('light-mode');
      if (moonIcon && sunIcon) {
        moonIcon.classList.add('hidden');
        sunIcon.classList.remove('hidden');
      }
      if (mobileMoonIcon && mobileSunIcon) {
        mobileMoonIcon.classList.add('hidden');
        mobileSunIcon.classList.remove('hidden');
      }
      localStorage.setItem('theme', 'light');
    }
  }

  // Add event listeners to theme toggles
  if (themeToggle) {
    themeToggle.addEventListener('click', toggleTheme);
  }

  if (mobileThemeToggle) {
    mobileThemeToggle.addEventListener('click', toggleTheme);
  }

  // Listen for system preference changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
    if (!localStorage.getItem('theme')) {
      if (e.matches) {
        document.documentElement.classList.add('dark-mode');
        document.documentElement.classList.remove('light-mode');
        moonIcon.classList.remove('hidden');
        sunIcon.classList.add('hidden');
        mobileMoonIcon.classList.remove('hidden');
        mobileSunIcon.classList.add('hidden');
      } else {
        document.documentElement.classList.remove('dark-mode');
        document.documentElement.classList.add('light-mode');
        moonIcon.classList.add('hidden');
        sunIcon.classList.remove('hidden');
        mobileMoonIcon.classList.add('hidden');
        mobileSunIcon.classList.remove('hidden');
      }
    }
  });

  // Mobile menu toggle functionality
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        window.scrollTo({
          top: target.offsetTop - 80,
          behavior: 'smooth'
        });
      }
    });
  });

  // Parallax effect for hero section
  const heroSection = document.querySelector('#home');
  if (heroSection) {
    window.addEventListener('scroll', function() {
      const scrollPosition = window.scrollY;
      heroSection.style.backgroundPosition = `center ${scrollPosition * 0.5}px`;
    });
  }

  // Add steady hover effects to product cards (no animations)
  const productCards = document.querySelectorAll('.bg-gray-800.rounded-lg');
  productCards.forEach(card => {
    // Add steady-card class to prevent animations
    card.classList.add('steady-card');

    // Simple hover effect without animations
    card.addEventListener('mouseenter', function() {
      // Add shadow effect without transform
      this.style.boxShadow = '0 0 15px 0 rgba(59, 130, 246, 0.3), 0 10px 25px -5px rgba(59, 130, 246, 0.5)';

      // Change button color only
      const cardButton = this.querySelector('a, button');
      if (cardButton) {
        cardButton.classList.add('bg-blue-500');
        cardButton.classList.remove('bg-blue-600');
      }
    });

    card.addEventListener('mouseleave', function() {
      // Remove shadow effect
      this.style.boxShadow = '0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';

      // Reset button color
      const cardButton = this.querySelector('a, button');
      if (cardButton) {
        cardButton.classList.remove('bg-blue-500');
        cardButton.classList.add('bg-blue-600');
      }
    });
  });

  // Add effects only to product card icons
  const productIcons = document.querySelectorAll('.bg-gray-800.rounded-lg .bg-blue-600.bg-opacity-20');
  productIcons.forEach(icon => {
    // Pulse animation on hover
    icon.addEventListener('mouseenter', function() {
      this.style.transform = 'scale(1.15)';
      this.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.6)';
      this.style.backgroundColor = 'rgba(59, 130, 246, 0.3)';
      this.style.transition = 'all 0.3s ease';

      // Add subtle rotation animation
      const svg = this.querySelector('svg');
      if (svg) {
        svg.style.transition = 'transform 0.5s ease';
        svg.style.transform = 'rotate(5deg)';
        svg.style.filter = 'drop-shadow(0 0 3px rgba(59, 130, 246, 0.8))';
      }
    });

    // Reset on mouse leave
    icon.addEventListener('mouseleave', function() {
      this.style.transform = '';
      this.style.boxShadow = '';
      this.style.backgroundColor = '';

      const svg = this.querySelector('svg');
      if (svg) {
        svg.style.transform = '';
        svg.style.filter = '';
      }
    });

    // Add subtle continuous pulse animation
    let growing = true;
    const pulseAnimation = setInterval(() => {
      const svg = icon.querySelector('svg');
      if (svg) {
        if (growing) {
          svg.style.transform = 'scale(1.05)';
          svg.style.filter = 'brightness(1.1)';
        } else {
          svg.style.transform = 'scale(1)';
          svg.style.filter = 'brightness(1)';
        }
        growing = !growing;
      }
    }, 2000);

    // Clean up interval when needed
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        clearInterval(pulseAnimation);
      }
    });
  });

  // Lazy load images
  const lazyImages = document.querySelectorAll('img[data-src]');
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      });
    });

    lazyImages.forEach(img => imageObserver.observe(img));
  }

  // Footer effects
  const footer = document.querySelector('footer');
  if (footer) {
    // Replace with a simpler, more reliable gradient effect
    footer.style.transition = 'background-position 8s ease-in-out';
    footer.style.backgroundSize = '200% 200%';
    footer.style.backgroundImage = 'linear-gradient(45deg, #0f172a, #1e3a8a, #0f172a, #1e3a8a)';

    let direction = 'right';
    const animateFooter = () => {
      if (direction === 'right') {
        footer.style.backgroundPosition = '100% 50%';
        direction = 'left';
      } else {
        footer.style.backgroundPosition = '0% 50%';
        direction = 'right';
      }
      setTimeout(animateFooter, 8000);
    };

    // Start the animation
    animateFooter();

    // Add hover effects to footer links
    const footerLinks = footer.querySelectorAll('a');
    footerLinks.forEach(link => {
      link.addEventListener('mouseenter', function() {
        this.classList.add('transition-all', 'duration-300');
        this.style.textShadow = '0 0 8px rgba(59, 130, 246, 0.8)';
        this.style.transform = 'translateY(-2px)';
      });

      link.addEventListener('mouseleave', function() {
        this.style.textShadow = '';
        this.style.transform = '';
      });
    });
  }

  // Product page specific effects
  const isProductPage = document.title.includes('DentalPro') ||
                        document.title.includes('PharmTrack') ||
                        document.title.includes('MedOffice');

  if (isProductPage) {
    // Add floating animation to product hero image
    const heroImage = document.querySelector('.pt-32 img');
    if (heroImage) {
      // Add floating animation
      heroImage.style.transition = 'transform 3s ease-in-out';

      // Create floating effect
      let floatUp = true;
      setInterval(() => {
        if (floatUp) {
          heroImage.style.transform = 'translateY(-10px)';
        } else {
          heroImage.style.transform = 'translateY(0)';
        }
        floatUp = !floatUp;
      }, 3000);
    }

    // Add highlight effect to feature icons
    const featureIcons = document.querySelectorAll('.bg-blue-600.bg-opacity-20');
    featureIcons.forEach(icon => {
      icon.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'rgba(59, 130, 246, 0.4)';
        this.style.transform = 'scale(1.1)';
        this.style.transition = 'all 0.3s ease';
      });

      icon.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
        this.style.transform = '';
      });
    });

    // Add scroll-triggered animations to sections
    const sections = document.querySelectorAll('section');
    window.addEventListener('scroll', () => {
      sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;

        if (sectionTop < windowHeight * 0.75 && sectionTop > -windowHeight * 0.5) {
          section.style.opacity = '1';
          section.style.transform = 'translateY(0)';
          section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        } else {
          section.style.opacity = '0.8';
          section.style.transform = 'translateY(20px)';
        }
      });
    });
  }

  // Enhanced navigation effects
  const nav = document.querySelector('nav');
  if (nav) {
    // Change navbar on scroll
    window.addEventListener('scroll', function() {
      const isDarkMode = document.documentElement.classList.contains('dark-mode');

      if (window.scrollY > 50) {
        nav.classList.add('py-2');
        nav.classList.add('shadow-xl');
        nav.classList.add('scrolled');

        // Let CSS handle the colors based on theme
      } else {
        nav.classList.remove('py-2');
        nav.classList.remove('shadow-xl');
        nav.classList.remove('scrolled');

        // Let CSS handle the colors based on theme
      }
    });

    // Add hover effect to nav links
    const navLinks = nav.querySelectorAll('a:not(.text-gradient)');
    navLinks.forEach(link => {
      link.addEventListener('mouseenter', function() {
        this.classList.add('scale-110');

        // Create underline effect
        if (!this.querySelector('.nav-underline')) {
          const underline = document.createElement('div');
          underline.classList.add('nav-underline');
          underline.style.height = '2px';
          underline.style.width = '0';
          underline.style.backgroundColor = '#3b82f6';
          underline.style.transition = 'width 0.3s ease';
          underline.style.marginTop = '2px';
          this.appendChild(underline);

          // Animate the underline
          setTimeout(() => {
            underline.style.width = '100%';
          }, 10);
        }
      });

      link.addEventListener('mouseleave', function() {
        this.classList.remove('scale-110');

        // Remove underline effect
        const underline = this.querySelector('.nav-underline');
        if (underline) {
          underline.style.width = '0';
          setTimeout(() => {
            underline.remove();
          }, 300);
        }
      });
    });

    // Add active state for current page
    const currentPage = window.location.pathname.split('/').pop();
    const activeLink = nav.querySelector(`a[href="${currentPage}"]`);
    if (activeLink) {
      activeLink.classList.add('text-blue-400');
      activeLink.classList.add('font-semibold');
    }
  }

  // Add effects to horizontal timeline dots
  const horizontalTimelineDots = document.querySelectorAll('.horizontal-timeline-dot');
  horizontalTimelineDots.forEach((dot, index) => {
    // Add hover effects
    dot.addEventListener('mouseenter', function() {
      this.style.transform = 'scale(1.2)';
      this.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.7)';
      this.style.transition = 'all 0.3s ease';

      const svg = this.querySelector('svg');
      if (svg) {
        svg.style.transform = 'rotate(15deg)';
        svg.style.transition = 'transform 0.5s ease';
      }

      // Highlight the corresponding content
      const timelineItem = this.closest('.horizontal-timeline-item');
      if (timelineItem) {
        const content = timelineItem.querySelector('.horizontal-timeline-content .bg-gray-800');
        if (content) {
          content.style.transform = 'translateY(-5px)';
          content.style.boxShadow = '0 15px 30px -5px rgba(0, 0, 0, 0.4), 0 15px 15px -5px rgba(0, 0, 0, 0.3), 0 0 20px 0 rgba(59, 130, 246, 0.3)';
          content.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
        }
      }
    });

    // Reset on mouse leave
    dot.addEventListener('mouseleave', function() {
      this.style.transform = '';
      this.style.boxShadow = '';

      const svg = this.querySelector('svg');
      if (svg) {
        svg.style.transform = '';
      }

      // Reset the corresponding content
      const timelineItem = this.closest('.horizontal-timeline-item');
      if (timelineItem) {
        const content = timelineItem.querySelector('.horizontal-timeline-content .bg-gray-800');
        if (content) {
          content.style.transform = '';
          content.style.boxShadow = '';
        }
      }
    });

    // Add subtle pulse animation with delay based on index
    setTimeout(() => {
      let pulsing = true;
      const pulseAnimation = setInterval(() => {
        if (pulsing) {
          dot.style.transform = 'scale(1.05)';
          dot.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.8)';
        } else {
          dot.style.transform = 'scale(1)';
          dot.style.boxShadow = '0 0 10px rgba(59, 130, 246, 0.5)';
        }
        pulsing = !pulsing;
      }, 2000);

      // Clean up interval when needed
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          clearInterval(pulseAnimation);
        }
      });
    }, index * 200); // Stagger the animations
  });

  // Add scroll functionality for horizontal timeline on mobile/tablet
  const horizontalTimelineContainer = document.querySelector('.horizontal-timeline-container');
  if (horizontalTimelineContainer) {
    // Add visual indicator for scrollable content on smaller screens
    if (window.innerWidth < 1024) {
      const scrollIndicator = document.createElement('div');
      scrollIndicator.className = 'scroll-indicator';
      scrollIndicator.innerHTML = '<span>Scroll to see more</span><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>';
      scrollIndicator.style.display = 'flex';
      scrollIndicator.style.alignItems = 'center';
      scrollIndicator.style.justifyContent = 'center';
      scrollIndicator.style.padding = '8px';
      scrollIndicator.style.marginTop = '10px';
      scrollIndicator.style.color = 'var(--text-secondary)';
      scrollIndicator.style.fontSize = '0.8rem';

      // Add animation to the indicator
      let scrollDirection = 'right';
      setInterval(() => {
        if (scrollDirection === 'right') {
          scrollIndicator.querySelector('svg').style.transform = 'translateX(3px)';
          scrollDirection = 'left';
        } else {
          scrollIndicator.querySelector('svg').style.transform = 'translateX(0)';
          scrollDirection = 'right';
        }
      }, 800);

      // Insert after the timeline container
      horizontalTimelineContainer.parentNode.insertBefore(scrollIndicator, horizontalTimelineContainer.nextSibling);

      // Hide indicator after user has scrolled
      horizontalTimelineContainer.addEventListener('scroll', function() {
        scrollIndicator.style.opacity = '0';
        scrollIndicator.style.transition = 'opacity 0.5s ease';
        setTimeout(() => {
          scrollIndicator.style.display = 'none';
        }, 500);
      }, { once: true });
    }
  }
});















