/**
 * CureOx Article Page
 * Handles dynamic article loading and interactions
 */

class ArticlePage {
  constructor() {
    this.articleData = {
      'featured': {
        title: 'The Digital Transformation of Healthcare: 2023 Outlook',
        category: 'Innovation',
        date: 'July 5, 2023',
        author: {
          name: 'Dr. <PERSON>',
          title: 'Healthcare Technology Specialist',
          image: 'images/author-avatar.png',
          bio: 'Dr. <PERSON> is a healthcare technology specialist with over 15 years of experience in digital health transformation. She has advised numerous healthcare organizations on implementing innovative technologies to improve patient care and operational efficiency.'
        },
        heroImage: 'images/blog-featured.jpg',
        content: `
          <p>The healthcare industry is experiencing an unprecedented digital revolution. As we move further into 2023, the pace of technological adoption continues to accelerate, driven by the lingering effects of the pandemic, changing patient expectations, and the promise of improved outcomes and efficiency.</p>

          <h2>1. AI-Powered Clinical Decision Support</h2>
          <p>Artificial intelligence is moving beyond administrative tasks to directly impact clinical care. Advanced algorithms are now capable of analyzing complex medical data, identifying patterns, and providing recommendations that support physician decision-making. These systems are particularly valuable in specialties like radiology, pathology, and oncology, where they can detect subtle abnormalities that might be missed by the human eye.</p>

          <h2>2. Remote Patient Monitoring Evolution</h2>
          <p>Remote patient monitoring has evolved from simple vital sign tracking to comprehensive health management platforms. The latest generation of RPM solutions incorporates wearable devices, smartphone apps, and home-based sensors to create a continuous stream of patient data. This information is automatically analyzed to detect concerning trends, allowing for early intervention before conditions worsen.</p>

          <h2>3. Interoperability Breakthroughs</h2>
          <p>After years of fragmentation, healthcare systems are finally making significant progress in sharing data seamlessly across different platforms and organizations. The implementation of FHIR (Fast Healthcare Interoperability Resources) standards and APIs is enabling a more connected healthcare ecosystem, benefiting both providers and patients.</p>

          <h2>4. Virtual Care Maturation</h2>
          <p>Telehealth is evolving from a pandemic necessity to a permanent fixture in healthcare delivery. The focus has shifted from basic video consultations to specialized virtual care models tailored to specific conditions and patient populations. Mental health, chronic disease management, and post-surgical follow-up are areas where virtual care is showing particular promise.</p>

          <h2>5. Digital Front Door Strategies</h2>
          <p>Healthcare providers are investing in comprehensive digital front door strategies that unify patient engagement across multiple channels. These platforms typically include online scheduling, virtual waiting rooms, secure messaging, bill payment, and access to health records, creating a seamless patient experience.</p>

          <h2>Conclusion</h2>
          <p>The digital transformation of healthcare is no longer a future trend—it's happening now, and the pace of change is accelerating. Organizations that embrace these technologies while maintaining a focus on the human elements of care will be best positioned to thrive in this new landscape. As we move through 2023 and beyond, we can expect to see continued innovation and integration of digital solutions across the healthcare ecosystem.</p>
        `,
        tags: ['Digital Transformation', 'AI', 'Remote Monitoring', 'Telehealth', 'Interoperability', 'Healthcare Technology'],
        relatedArticles: ['1', '2', '3']
      },
      '1': {
        title: 'The Future of AI in Healthcare Diagnostics',
        category: 'AI & Machine Learning',
        date: 'June 15, 2023',
        author: {
          name: 'Dr. Michael Chen',
          title: 'AI Research Director',
          image: 'images/author-avatar2.png',
          bio: 'Dr. Michael Chen leads AI research initiatives focused on healthcare applications. With a background in both medicine and computer science, he specializes in developing machine learning models for diagnostic imaging and clinical decision support systems.'
        },
        heroImage: 'images/blog-ai-healthcare.webp',
        content: `
          <p>Artificial intelligence is revolutionizing healthcare diagnostics, offering unprecedented accuracy, speed, and insights that were previously impossible. As we look to the future, AI's role in disease detection and diagnosis will only expand, potentially transforming how healthcare is delivered worldwide.</p>

          <h2>Early Disease Detection</h2>
          <p>AI algorithms can analyze medical images, genetic data, and patient records to identify patterns that may indicate early-stage diseases. This capability is particularly valuable for conditions like cancer, where early detection significantly improves outcomes.</p>

          <h2>Diagnostic Accuracy</h2>
          <p>Machine learning models trained on vast datasets can achieve diagnostic accuracy that rivals or exceeds human specialists in certain domains. These systems can help reduce diagnostic errors and provide consistent results across different healthcare settings.</p>

          <h2>Personalized Medicine</h2>
          <p>AI can analyze a patient's unique genetic makeup, medical history, and lifestyle factors to recommend personalized treatment plans with higher efficacy and fewer side effects.</p>

          <h2>Challenges and Considerations</h2>
          <p>Despite its promise, AI in healthcare diagnostics faces several challenges, including regulatory hurdles, integration with existing workflows, and concerns about bias and transparency. Addressing these issues will be crucial for widespread adoption.</p>

          <h2>The Road Ahead</h2>
          <p>As AI technology continues to advance, we can expect to see more sophisticated diagnostic tools that combine multiple data sources, provide real-time insights, and seamlessly integrate with clinical workflows. The future of healthcare diagnostics is not about replacing human clinicians but augmenting their capabilities to deliver better patient care.</p>
        `,
        tags: ['AI', 'Machine Learning', 'Diagnostics', 'Early Detection', 'Personalized Medicine'],
        relatedArticles: ['featured', '2', '3']
      },
      '2': {
        title: '5 Telemedicine Trends Reshaping Patient Care',
        category: 'Telemedicine',
        date: 'May 28, 2023',
        author: {
          name: 'Jessica Martinez',
          title: 'Digital Health Strategist',
          image: 'images/author-avatar3.jpg',
          bio: 'Jessica Martinez is a digital health strategist who helps healthcare organizations develop and implement effective telehealth programs. She has worked with hospitals, clinics, and health systems across the country to optimize virtual care delivery.'
        },
        heroImage: 'images/blog-telemedicine.jpg',
        content: `
          <p>Telemedicine has evolved from a convenient alternative to an essential component of modern healthcare delivery. The following trends are reshaping how patients receive care and how providers deliver services in the digital age.</p>

          <h2>1. Hybrid Care Models</h2>
          <p>Healthcare providers are increasingly adopting hybrid care models that combine in-person visits with virtual consultations. This approach offers the best of both worlds, allowing for physical examinations when necessary while providing convenient follow-ups and routine care virtually.</p>

          <h2>2. Specialized Telemedicine Platforms</h2>
          <p>Rather than one-size-fits-all solutions, specialized telemedicine platforms are emerging for specific medical specialties like dermatology, mental health, and chronic disease management, offering tailored features and workflows.</p>

          <h2>3. Remote Patient Monitoring Integration</h2>
          <p>Telemedicine platforms are increasingly integrating with remote monitoring devices, allowing providers to collect and analyze patient data between virtual visits for more comprehensive care.</p>

          <h2>4. AI-Enhanced Virtual Visits</h2>
          <p>Artificial intelligence is being incorporated into telemedicine platforms to improve efficiency and effectiveness. AI can help with pre-visit screening, documentation, and even preliminary diagnoses, allowing providers to focus more on patient interaction.</p>

          <h2>5. Expanded Reimbursement Models</h2>
          <p>As telemedicine becomes a standard part of healthcare delivery, payment models are evolving to support virtual care. Many insurers now offer permanent coverage for telehealth services, and value-based care models are increasingly incorporating virtual care components.</p>
        `,
        tags: ['Telemedicine', 'Virtual Care', 'Remote Monitoring', 'Healthcare Technology', 'Digital Health'],
        relatedArticles: ['featured', '1', '3']
      },
      '3': {
        title: 'Securing Patient Data: Best Practices for Healthcare Providers',
        category: 'Data Security',
        date: 'April 10, 2023',
        author: {
          name: 'Robert Williams',
          title: 'Healthcare Cybersecurity Expert',
          image: 'images/author-avatar4.png',
          bio: 'Robert Williams is a cybersecurity expert specializing in healthcare data protection. He has helped numerous healthcare organizations implement robust security measures to safeguard sensitive patient information while maintaining compliance with regulatory requirements.'
        },
        heroImage: 'images/blog-data-security.jpg',
        content: `
          <p>As healthcare becomes increasingly digital, protecting patient data has never been more critical. Healthcare organizations face unique cybersecurity challenges, from regulatory compliance to the high value of medical records on the black market.</p>

          <h2>Comprehensive Risk Assessment</h2>
          <p>Regular security risk assessments are essential for identifying vulnerabilities in your systems and processes. These assessments should evaluate technical infrastructure, administrative procedures, and physical safeguards.</p>

          <h2>Employee Training and Awareness</h2>
          <p>Human error remains one of the biggest security risks. Implementing ongoing security awareness training for all staff members is crucial for preventing phishing attacks, social engineering, and other common threats.</p>

          <h2>Encryption and Access Controls</h2>
          <p>Implementing strong encryption for data at rest and in transit, along with role-based access controls, helps ensure that sensitive information is only accessible to authorized personnel.</p>

          <h2>Incident Response Planning</h2>
          <p>Despite best efforts, security incidents can still occur. Having a well-defined incident response plan allows organizations to quickly detect, contain, and mitigate the impact of a breach.</p>

          <h2>Vendor Management</h2>
          <p>Many healthcare organizations rely on third-party vendors for various services. Implementing a robust vendor management program helps ensure that these partners maintain appropriate security controls and don't introduce unnecessary risks.</p>
        `,
        tags: ['Cybersecurity', 'Data Protection', 'HIPAA', 'Risk Assessment', 'Healthcare IT'],
        relatedArticles: ['featured', '1', '2']
      }
    };
    
    this.initializeArticle();
    this.initializeScrollProgress();
  }

  /**
   * Initialize article content based on URL parameter
   */
  initializeArticle() {
    // Get article ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const articleId = urlParams.get('id') || 'featured';
    
    // Get article data
    const article = this.articleData[articleId];
    
    if (!article) {
      this.showArticleNotFound();
      return;
    }
    
    // Update page title
    document.title = `${article.title} - CureOx Blog`;
    
    // Update article hero
    document.getElementById('article-hero').style.backgroundImage = `url('${article.heroImage}')`;
    document.getElementById('article-category').textContent = article.category;
    document.getElementById('article-date').textContent = article.date;
    document.getElementById('article-title').textContent = article.title;
    
    // Update author info
    document.getElementById('author-image').src = article.author.image;
    document.getElementById('author-name').textContent = article.author.name;
    document.getElementById('author-title').textContent = article.author.title;
    
    // Update article content
    document.getElementById('article-content').innerHTML = article.content;
    
    // Update article tags
    const tagsContainer = document.getElementById('article-tags');
    tagsContainer.innerHTML = '';
    
    article.tags.forEach(tag => {
      const tagElement = document.createElement('span');
      tagElement.className = 'px-3 py-1 bg-gray-800 rounded-full text-sm text-gray-300 hover:bg-blue-600 hover:text-white transition-all cursor-pointer';
      tagElement.textContent = tag;
      tagsContainer.appendChild(tagElement);
    });
    
    // Update author bio
    document.getElementById('author-bio-image').src = article.author.image;
    document.getElementById('author-bio-name').textContent = article.author.name;
    document.getElementById('author-bio-title').textContent = article.author.title;
    document.getElementById('author-bio-description').textContent = article.author.bio;
    
    // Update related articles
    this.loadRelatedArticles(article.relatedArticles);
  }

  /**
   * Load related articles
   * @param {Array} relatedIds - Array of related article IDs
   */
  loadRelatedArticles(relatedIds) {
    const relatedContainer = document.getElementById('related-articles');
    relatedContainer.innerHTML = '';
    
    relatedIds.forEach(id => {
      const article = this.articleData[id];
      if (!article) return;
      
      const articleElement = document.createElement('div');
      articleElement.className = 'related-article bg-gray-800 rounded-xl overflow-hidden shadow-lg';
      articleElement.innerHTML = `
        <div class="relative">
          <img src="${article.heroImage}" alt="${article.title}" class="w-full h-48 object-cover">
          <div class="absolute top-4 right-4 bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-3 py-1 rounded-full">${article.category}</div>
        </div>
        <div class="p-6">
          <div class="flex items-center mb-3">
            <img src="${article.author.image}" alt="${article.author.name}" class="w-8 h-8 rounded-full mr-3">
            <span class="text-sm text-gray-400">${article.author.name} • ${article.date}</span>
          </div>
          <h3 class="text-xl font-bold mb-3">${article.title}</h3>
          <a href="article.html?id=${id}" class="group inline-flex items-center text-blue-400 font-medium hover:text-blue-300 transition-all">
            Read Article
            <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </a>
        </div>
      `;
      
      relatedContainer.appendChild(articleElement);
    });
  }

  /**
   * Show article not found message
   */
  showArticleNotFound() {
    document.getElementById('article-title').textContent = 'Article Not Found';
    document.getElementById('article-content').innerHTML = `
      <div class="text-center py-12">
        <svg class="w-24 h-24 mx-auto text-gray-500 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h2 class="text-2xl font-bold mb-4">Article Not Found</h2>
        <p class="text-gray-400 mb-6">The article you're looking for doesn't exist or has been removed.</p>
        <a href="blog-redesign.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-all inline-flex items-center">
          Return to Blog
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
        </a>
      </div>
    `;
  }

  /**
   * Initialize scroll progress indicator
   */
  initializeScrollProgress() {
    const progressBar = document.getElementById('article-progress');
    
    window.addEventListener('scroll', () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      
      progressBar.style.width = scrollPercent + '%';
    });
  }
}

// Initialize article page when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.articlePage = new ArticlePage();
});
