/**
 * CureOx Blog Features
 * Implements advanced blog functionality including search, filtering, and recommendations
 */

class BlogFeatures {
  constructor() {
    this.initializeSearch();
    this.initializeFilters();
    this.initializeRelatedArticles();
    this.initializeNewsletterSubscription();
  }

  /**
   * Initialize blog search functionality
   */
  initializeSearch() {
    const searchForm = document.getElementById('blog-search-form');
    const searchInput = document.getElementById('blog-search-input');
    const searchResults = document.getElementById('blog-search-results');
    const blogPosts = document.querySelectorAll('.blog-post');
    
    if (searchForm && searchInput && searchResults) {
      // Handle search form submission
      searchForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.performSearch(searchInput.value, blogPosts, searchResults);
      });
      
      // Real-time search as user types (with debounce)
      let debounceTimer;
      searchInput.addEventListener('input', () => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
          this.performSearch(searchInput.value, blogPosts, searchResults);
        }, 300);
      });
    }
  }

  /**
   * Perform search on blog posts
   */
  performSearch(query, blogPosts, resultsContainer) {
    // Clear previous results
    resultsContainer.innerHTML = '';
    
    if (!query.trim()) {
      // If search is empty, hide results and show all posts
      resultsContainer.classList.add('hidden');
      blogPosts.forEach(post => {
        post.closest('.blog-post-container').classList.remove('hidden');
      });
      return;
    }
    
    // Show results container
    resultsContainer.classList.remove('hidden');
    
    // Convert query to lowercase for case-insensitive search
    const searchQuery = query.toLowerCase();
    let matchCount = 0;
    
    // Search through blog posts
    blogPosts.forEach(post => {
      const postContainer = post.closest('.blog-post-container');
      const title = post.querySelector('h3')?.textContent.toLowerCase() || '';
      const content = post.querySelector('p')?.textContent.toLowerCase() || '';
      const category = post.getAttribute('data-category')?.toLowerCase() || '';
      const author = post.querySelector('.author-name')?.textContent.toLowerCase() || '';
      
      // Check if post matches search query
      if (title.includes(searchQuery) || 
          content.includes(searchQuery) || 
          category.includes(searchQuery) || 
          author.includes(searchQuery)) {
        // Show matching post
        postContainer.classList.remove('hidden');
        matchCount++;
        
        // Highlight matching text in the post
        this.highlightText(post, searchQuery);
      } else {
        // Hide non-matching post
        postContainer.classList.add('hidden');
      }
    });
    
    // Display search results summary
    if (matchCount > 0) {
      resultsContainer.innerHTML = `<div class="mb-4 text-blue-400">Found ${matchCount} result${matchCount !== 1 ? 's' : ''} for "${query}"</div>`;
    } else {
      resultsContainer.innerHTML = `
        <div class="mb-4 text-blue-400">No results found for "${query}"</div>
        <p class="text-gray-300 mb-4">Try different keywords or browse by category below.</p>
      `;
    }
  }

  /**
   * Highlight matching text in search results
   */
  highlightText(element, query) {
    const titleElement = element.querySelector('h3');
    const contentElement = element.querySelector('p');
    
    if (titleElement) {
      const originalTitle = titleElement.textContent;
      const lowerTitle = originalTitle.toLowerCase();
      const queryIndex = lowerTitle.indexOf(query.toLowerCase());
      
      if (queryIndex !== -1) {
        const before = originalTitle.substring(0, queryIndex);
        const match = originalTitle.substring(queryIndex, queryIndex + query.length);
        const after = originalTitle.substring(queryIndex + query.length);
        
        titleElement.innerHTML = `${before}<span class="bg-blue-600 bg-opacity-30 px-1">${match}</span>${after}`;
      }
    }
    
    if (contentElement) {
      const originalContent = contentElement.textContent;
      const lowerContent = originalContent.toLowerCase();
      const queryIndex = lowerContent.indexOf(query.toLowerCase());
      
      if (queryIndex !== -1) {
        const before = originalContent.substring(0, queryIndex);
        const match = originalContent.substring(queryIndex, queryIndex + query.length);
        const after = originalContent.substring(queryIndex + query.length);
        
        contentElement.innerHTML = `${before}<span class="bg-blue-600 bg-opacity-30 px-1">${match}</span>${after}`;
      }
    }
  }

  /**
   * Initialize category filtering
   */
  initializeFilters() {
    const categoryFilters = document.querySelectorAll('.category-filter');
    const blogPosts = document.querySelectorAll('.blog-post');
    const activeFilterIndicator = document.getElementById('active-filter');
    
    if (categoryFilters.length > 0) {
      categoryFilters.forEach(filter => {
        filter.addEventListener('click', (e) => {
          e.preventDefault();
          
          // Remove active class from all filters
          categoryFilters.forEach(f => f.classList.remove('active', 'bg-blue-600'));
          
          // Add active class to clicked filter
          filter.classList.add('active', 'bg-blue-600');
          
          const category = filter.getAttribute('data-category');
          
          // Update active filter indicator
          if (activeFilterIndicator) {
            activeFilterIndicator.textContent = category === 'all' ? 'All Categories' : category;
          }
          
          // Filter blog posts
          blogPosts.forEach(post => {
            const postContainer = post.closest('.blog-post-container');
            const postCategory = post.getAttribute('data-category');
            
            if (category === 'all' || postCategory === category) {
              postContainer.classList.remove('hidden');
              // Add fade-in animation
              postContainer.classList.add('animate-fade-in');
              setTimeout(() => {
                postContainer.classList.remove('animate-fade-in');
              }, 500);
            } else {
              postContainer.classList.add('hidden');
            }
          });
          
          // Track user preference if available
          if (window.userPreferences && category !== 'all') {
            const preferences = window.userPreferences;
            if (!preferences.getBlogInterests().includes(category)) {
              preferences.preferences.blogCategories.push(category);
              preferences.savePreferences();
            }
          }
        });
      });
    }
  }

  /**
   * Initialize related articles functionality
   */
  initializeRelatedArticles() {
    const articleContainers = document.querySelectorAll('[id$="-full"]');
    
    articleContainers.forEach(container => {
      // Find the category of the current article
      const articleCategory = container.getAttribute('data-category');
      
      if (articleCategory) {
        // Find related articles section or create one if it doesn't exist
        let relatedSection = container.querySelector('.related-articles');
        
        if (!relatedSection) {
          relatedSection = document.createElement('div');
          relatedSection.className = 'related-articles mt-12 pt-8 border-t border-gray-700';
          
          const heading = document.createElement('h3');
          heading.className = 'text-2xl font-bold mb-6';
          heading.textContent = 'Related Articles';
          
          const articlesGrid = document.createElement('div');
          articlesGrid.className = 'grid grid-cols-1 md:grid-cols-2 gap-6';
          articlesGrid.id = 'related-articles-container';
          
          relatedSection.appendChild(heading);
          relatedSection.appendChild(articlesGrid);
          container.appendChild(relatedSection);
        }
        
        // Find all blog posts with the same category
        const relatedPosts = document.querySelectorAll(`.blog-post[data-category="${articleCategory}"]`);
        const relatedContainer = document.getElementById('related-articles-container');
        
        if (relatedContainer && relatedPosts.length > 0) {
          // Get current article ID
          const currentArticleId = container.id;
          
          // Add up to 2 related articles
          let count = 0;
          relatedPosts.forEach(post => {
            const postId = post.id;
            
            // Skip the current article
            if (postId && !currentArticleId.includes(postId) && count < 2) {
              const clone = post.cloneNode(true);
              
              // Update links to point to the full article
              const links = clone.querySelectorAll('a');
              links.forEach(link => {
                if (link.getAttribute('href').startsWith('#')) {
                  link.setAttribute('href', `#${postId}-full`);
                }
              });
              
              relatedContainer.appendChild(clone);
              count++;
            }
          });
          
          // If we didn't find enough related articles by category, add some based on recency
          if (count < 2) {
            const allPosts = document.querySelectorAll('.blog-post');
            
            allPosts.forEach(post => {
              const postId = post.id;
              
              // Skip the current article and already added related articles
              if (postId && 
                  !currentArticleId.includes(postId) && 
                  !relatedContainer.querySelector(`#${postId}`) && 
                  count < 2) {
                const clone = post.cloneNode(true);
                
                // Update links to point to the full article
                const links = clone.querySelectorAll('a');
                links.forEach(link => {
                  if (link.getAttribute('href').startsWith('#')) {
                    link.setAttribute('href', `#${postId}-full`);
                  }
                });
                
                relatedContainer.appendChild(clone);
                count++;
              }
            });
          }
        }
      }
    });
  }

  /**
   * Initialize newsletter subscription with personalization
   */
  initializeNewsletterSubscription() {
    const newsletterForm = document.getElementById('newsletter-form');
    const emailInput = document.getElementById('newsletter-email');
    const interestCheckboxes = document.querySelectorAll('.newsletter-interest');
    
    if (newsletterForm) {
      // Pre-select interests based on user preferences
      if (window.userPreferences) {
        const userInterests = window.userPreferences.getBlogInterests();
        
        interestCheckboxes.forEach(checkbox => {
          const interest = checkbox.value;
          if (userInterests.includes(interest)) {
            checkbox.checked = true;
          }
        });
      }
      
      // Handle form submission
      newsletterForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        // Get selected interests
        const selectedInterests = [];
        interestCheckboxes.forEach(checkbox => {
          if (checkbox.checked) {
            selectedInterests.push(checkbox.value);
          }
        });
        
        // Store subscription data (in a real implementation, this would be sent to a server)
        const subscriptionData = {
          email: emailInput.value,
          interests: selectedInterests,
          timestamp: new Date().toISOString()
        };
        
        // Store in localStorage for demo purposes
        const subscriptions = JSON.parse(localStorage.getItem('newsletter_subscriptions') || '[]');
        subscriptions.push(subscriptionData);
        localStorage.setItem('newsletter_subscriptions', JSON.stringify(subscriptions));
        
        // Update user preferences if available
        if (window.userPreferences) {
          selectedInterests.forEach(interest => {
            if (!window.userPreferences.getBlogInterests().includes(interest)) {
              window.userPreferences.preferences.blogCategories.push(interest);
            }
          });
          window.userPreferences.savePreferences();
        }
        
        // Show success message
        const successMessage = document.createElement('div');
        successMessage.className = 'bg-green-600 bg-opacity-20 border border-green-500 text-green-400 px-4 py-3 rounded mt-4';
        successMessage.innerHTML = `
          <p class="font-medium">Thank you for subscribing!</p>
          <p class="text-sm">You'll receive personalized content based on your interests.</p>
        `;
        
        newsletterForm.innerHTML = '';
        newsletterForm.appendChild(successMessage);
      });
    }
  }
}

// Initialize blog features when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (document.querySelector('.blog-section') || window.location.pathname.includes('blog')) {
    window.blogFeatures = new BlogFeatures();
  }
});
