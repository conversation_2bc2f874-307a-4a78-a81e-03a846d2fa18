<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Request Demo - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="styles/theme.css" rel="stylesheet">
  <style>
    .steady-card {
      transform: none !important;
      transition: none !important;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
    .steady-card:hover {
      transform: none !important;
      transition: none !important;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg fixed w-full z-10 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          <div class="hidden md:block ml-10">
            <div class="flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="demos/dentalpro-demo.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management System</a>
              <a href="demos/pharmtrack-demo.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management Program</a>
              <a href="demos/medoffice-demo.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management Program</a>
              <a href="demos/patient-app-demo.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
              <a href="pricing.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pricing</a>
            </div>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
                <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                </svg>
                <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
            <!-- Request Demo button removed -->
          </div>
        </div>
        <div class="-mr-2 flex md:hidden">
          <button type="button" class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
      <div class="hidden mobile-menu md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
          <a href="demos/dentalpro-demo.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management System</a>
          <a href="demos/pharmtrack-demo.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management Program</a>
          <a href="demos/medoffice-demo.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management Program</a>
          <a href="demos/patient-app-demo.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
          <a href="pricing.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pricing</a>
          <div class="flex items-center justify-between px-3 py-2">
            <span class="text-gray-300">Dark/Light Mode</span>
            <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <!-- Sun icon path -->
              </svg>
            </button>
          </div>
          <!-- Mobile Request Demo button removed -->
        </div>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="demo-hero" class="pt-32 pb-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-6" data-aos="fade-up">Request a <span class="text-gradient">Personalized Demo</span></h1>
        <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">Experience the power of CureOx healthcare solutions firsthand. Our team will walk you through a personalized demonstration tailored to your practice's needs.</p>
      </div>
    </div>
  </section>

  <!-- Demo Request Form Section -->
  <section class="py-16 bg-gray-900">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
        <!-- Form Column -->
        <div class="bg-gray-800 p-8 rounded-lg shadow-lg steady-card">
          <h2 class="text-2xl font-bold mb-6">Schedule Your Demo</h2>
          <form id="demo-form" class="space-y-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Full Name *</label>
              <input type="text" id="name" name="name" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email Address *</label>
              <input type="email" id="email" name="email" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
              <input type="tel" id="phone" name="phone" class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
              <label for="organization" class="block text-sm font-medium text-gray-300 mb-1">Organization Name *</label>
              <input type="text" id="organization" name="organization" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
              <label for="role" class="block text-sm font-medium text-gray-300 mb-1">Your Role</label>
              <select id="role" name="role" class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark-select">
                <option value="">Select your role</option>
                <option value="Doctor">Doctor</option>
                <option value="Dentist">Dentist</option>
                <option value="Pharmacist">Pharmacist</option>
                <option value="Practice Manager">Practice Manager</option>
                <option value="IT Administrator">IT Administrator</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div>
              <label for="product" class="block text-sm font-medium text-gray-300 mb-1">Product of Interest *</label>
              <select id="product" name="product" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark-select">
                <option value="">Select a product</option>
                <option value="Dental Clinic Management System">Dental Clinic Management System</option>
                <option value="Pharmacy Management Program">Pharmacy Management Program</option>
                <option value="Medical Clinic Management Program">Medical Clinic Management Program</option>
                <option value="Patient-Centric App">Patient-Centric App</option>
                <option value="Multiple">Multiple Products</option>
              </select>
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-300 mb-1">Additional Information</label>
              <textarea id="message" name="message" rows="4" class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>

            <div>
              <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-all">
                Schedule Demo
              </button>
            </div>
          </form>
        </div>

        <!-- Info Column -->
        <div data-aos="fade-left">
          <div class="bg-gray-800 p-8 rounded-lg shadow-lg mb-8">
            <h2 class="text-2xl font-bold mb-6">What to Expect</h2>
            <ul class="space-y-4">
              <li class="flex items-start">
                <svg class="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-gray-300">Personalized 30-minute demonstration</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-gray-300">Overview of key features relevant to your practice</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-gray-300">Q&A session with our product specialists</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-gray-300">Custom pricing proposal for your organization</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-gray-300">No obligation or pressure to purchase</span>
              </li>
            </ul>
          </div>

          <div class="bg-gray-800 p-8 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold mb-6">Why Choose CureOx?</h2>
            <div class="space-y-6">
              <div class="flex items-start">
                <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full flex-shrink-0 mr-4">
                  <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold mb-1">Specialized Solutions</h3>
                  <p class="text-gray-300">Purpose-built software for dentists, pharmacists, and medical practices.</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full flex-shrink-0 mr-4">
                  <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold mb-1">HIPAA Compliant</h3>
                  <p class="text-gray-300">Enterprise-grade security to protect sensitive patient data.</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full flex-shrink-0 mr-4">
                  <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold mb-1">Continuous Improvement</h3>
                  <p class="text-gray-300">Regular updates and new features based on user feedback and industry trends.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="script.js"></script>
</body>
</html>













