<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Article - CureOx Blog</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <link href="styles/theme.css" rel="stylesheet">
  <style>
    .article-content {
      max-width: 800px;
      margin: 0 auto;
    }

    .article-content p {
      margin-bottom: 1.5rem;
      line-height: 1.8;
    }

    .article-content h2 {
      font-size: 1.875rem;
      font-weight: 700;
      margin-top: 2.5rem;
      margin-bottom: 1.25rem;
    }

    .article-content h3 {
      font-size: 1.5rem;
      font-weight: 700;
      margin-top: 2rem;
      margin-bottom: 1rem;
    }

    .article-content ul, .article-content ol {
      margin-bottom: 1.5rem;
      padding-left: 1.5rem;
    }

    .article-content li {
      margin-bottom: 0.5rem;
    }

    .article-content img {
      border-radius: 0.5rem;
      margin: 2rem 0;
    }

    .article-content blockquote {
      border-left: 4px solid #3b82f6;
      padding-left: 1rem;
      font-style: italic;
      margin: 1.5rem 0;
    }

    .article-hero {
      height: 500px;
      background-size: cover;
      background-position: center;
      position: relative;
    }

    .article-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(15, 23, 42, 0.5), rgba(15, 23, 42, 0.9));
    }

    .article-hero-content {
      position: relative;
      z-index: 10;
    }

    .related-article {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .related-article:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    }

    .author-badge {
      border: 2px solid #3b82f6;
    }

    .article-progress {
      position: fixed;
      top: 0;
      left: 0;
      width: 0%;
      height: 3px;
      background: linear-gradient(to right, #3b82f6, #8b5cf6);
      z-index: 100;
      transition: width 0.1s ease;
    }
  </style>
</head>
<body>
  <!-- Reading Progress Bar -->
  <div class="article-progress" id="article-progress"></div>

  <!-- Navbar -->
  <nav class="fixed w-full z-50 bg-opacity-90 bg-dark-blue backdrop-filter backdrop-blur-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          </div>
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="index.html#about" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">About Us</a>
              <a href="index.html#services" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Services</a>
              <a href="index.html#products" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Products</a>
              <a href="blog-redesign.html" class="px-3 py-2 rounded-md text-sm font-medium text-white bg-blue-700 transition-all">Blog</a>
            </div>
          </div>
        </div>
        <!-- Theme toggle and contact button -->
        <div class="hidden md:flex items-center space-x-4">
          <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
          <a href="index.html#contact" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Contact Us</a>
        </div>
        <div class="md:hidden">
          <button type="button" id="mobile-menu-button" class="text-gray-300 hover:text-white">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Home</a>
        <a href="index.html#about" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">About Us</a>
        <a href="index.html#services" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Services</a>
        <a href="index.html#products" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Products</a>
        <a href="blog-redesign.html" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-blue-700">Blog</a>
      </div>
    </div>
  </nav>

  <!-- Article Hero -->
  <div class="article-hero pt-32" id="article-hero">
    <div class="article-hero-content h-full flex items-center">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="flex items-center mb-4">
          <span id="article-category" class="bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-3 py-1 rounded-full">Category</span>
          <span id="article-date" class="text-gray-400 text-sm ml-3">Date</span>
        </div>
        <h1 id="article-title" class="text-4xl md:text-5xl font-bold mb-6 leading-tight">Article Title</h1>

        <div class="flex items-center">
          <img id="author-image" src="images/author-avatar.png" alt="Author" class="w-12 h-12 rounded-full author-badge mr-4">
          <div>
            <div id="author-name" class="font-medium">Author Name</div>
            <div id="author-title" class="text-gray-400 text-sm">Author Title</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Article Content -->
  <section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="article-content text-gray-300" id="article-content">
        <!-- Article content will be loaded dynamically -->
        <p>Loading article content...</p>
      </div>

      <!-- Article Tags -->
      <div class="mt-12 pt-8 border-t border-gray-800">
        <div class="flex flex-wrap gap-2" id="article-tags">
          <!-- Tags will be loaded dynamically -->
        </div>
      </div>

      <!-- Author Bio -->
      <div class="mt-12 bg-gray-800 rounded-xl p-6 md:p-8" id="author-bio">
        <div class="md:flex items-center">
          <img id="author-bio-image" src="images/author-avatar.png" alt="Author" class="w-24 h-24 rounded-full author-badge mx-auto md:mx-0 md:mr-6 mb-4 md:mb-0">
          <div>
            <h3 id="author-bio-name" class="text-xl font-bold mb-2">Author Name</h3>
            <p id="author-bio-title" class="text-gray-400 mb-4">Author Title</p>
            <p id="author-bio-description" class="text-gray-300">Author bio will be loaded dynamically.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Related Articles -->
  <section class="py-16 bg-gray-900 bg-opacity-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold mb-10">Related Articles</h2>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8" id="related-articles">
        <!-- Related articles will be loaded dynamically -->
      </div>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="py-16 bg-gradient-to-r from-blue-900 to-indigo-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-3xl font-bold mb-4">Subscribe to Our Newsletter</h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">Stay updated with the latest insights and trends in healthcare technology.</p>

        <form id="newsletter-form" class="max-w-lg mx-auto">
          <div class="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              id="newsletter-email"
              placeholder="Enter your email"
              required
              class="flex-grow px-4 py-3 rounded-md bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
            <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-all whitespace-nowrap">
              Subscribe
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="md:col-span-1">
          <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
          <div class="flex space-x-4 mt-6">
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.205.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.849-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.203-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="md:col-span-1">
          <h3 class="text-lg font-bold mb-4 text-white">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="index.html" class="text-gray-300 hover:text-blue-400 transition-all">Home</a></li>
            <li><a href="index.html#about" class="text-gray-300 hover:text-blue-400 transition-all">About Us</a></li>
            <li><a href="index.html#services" class="text-gray-300 hover:text-blue-400 transition-all">Services</a></li>
            <li><a href="index.html#products" class="text-gray-300 hover:text-blue-400 transition-all">Products</a></li>
            <li><a href="blog-redesign.html" class="text-gray-300 hover:text-blue-400 transition-all">Blog</a></li>
          </ul>
        </div>

        <!-- Contact -->
        <div class="md:col-span-1">
          <h3 class="text-lg font-bold mb-4 text-white">Contact Us</h3>
          <div class="space-y-4">
            <div class="flex items-start">
              <svg class="h-6 w-6 text-blue-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <p class="text-gray-300">United States</p>
            </div>
            <div class="flex items-start">
              <svg class="h-6 w-6 text-blue-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <p class="text-gray-300"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2025 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Initialize AOS -->
  <script>
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: false,
      mirror: true
    });
  </script>
  <script src="script.js"></script>
  <script src="js/user-preferences.js"></script>
  <script src="js/article.js"></script>

