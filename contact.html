<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <link href="styles/theme.css" rel="stylesheet">
  <style>
    .steady-card {
      transform: none !important;
      transition: none !important;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
    .steady-card:hover {
      transform: none !important;
      transition: none !important;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="fixed w-full z-50 backdrop-filter backdrop-blur-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          </div>
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="index.html#about" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">About Us</a>
              <a href="index.html#services" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Services</a>
              <a href="index.html#products" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Products</a>
              <a href="blog.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Blog</a>
              <a href="contact.html" class="px-3 py-2 rounded-md text-sm font-medium text-white bg-blue-700 transition-all">Contact</a>
            </div>
          </div>
        </div>
        <!-- Add theme toggle -->
        <div class="hidden md:flex items-center space-x-4">
          <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button id="mobile-menu-button" class="text-gray-400 hover:text-white focus:outline-none">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Home</a>
        <a href="index.html#about" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">About Us</a>
        <a href="index.html#services" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Services</a>
        <a href="index.html#products" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Products</a>
        <a href="blog.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Blog</a>
        <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-blue-700">Contact</a>
        <div class="flex items-center justify-between px-3 py-2">
          <span class="text-gray-300">Dark/Light Mode</span>
          <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Contact Header -->
  <section class="pt-32 pb-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">Get in <span class="text-gradient">Touch</span></h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">For inquiries and further information, we are delighted to provide more details about our services and the solutions we offer. we look forward to collaborating with you to achieve a comprehensive vision for healthcare development.</p>
      </div>
    </div>
  </section>

  <!-- Contact Form Section -->
  <section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
        <!-- Contact Information -->
        <div data-aos="fade-right">
          <h2 class="text-2xl font-bold mb-6">Contact Information</h2>
          <div class="space-y-6">
            <div class="flex items-start">
              <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full mr-4">
                <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-medium mb-1">Our Location</h3>
                <p class="text-gray-300">United States</p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full mr-4">
                <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-medium mb-1">Phone Number</h3>
                <p class="text-gray-300">+359 87 760 8877</p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full mr-4">
                <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-medium mb-1">Email Address</h3>
                <p class="text-gray-300"><EMAIL></p>
              </div>
            </div>
          </div>

          <div class="mt-10">
            <h3 class="text-xl font-bold mb-4">Connect With Us</h3>
            <div class="flex space-x-4">
              <a href="#" class="bg-blue-800 p-3 rounded-full text-white hover:bg-blue-600 transition-all">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" class="bg-blue-800 p-3 rounded-full text-white hover:bg-blue-600 transition-all">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.054 10.054 0 01-3.127 1.184 4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
              <a href="#" class="bg-blue-800 p-3 rounded-full text-white hover:bg-blue-600 transition-all">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="bg-gray-800 p-8 rounded-lg shadow-lg steady-card" data-aos="fade-left">
          <h2 class="text-2xl font-bold mb-6">Send Us a Message</h2>
          <form id="contact-form" action="#" method="POST">
            <div class="grid grid-cols-1 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Your Name</label>
                <input type="text" id="name" name="name" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email Address</label>
                <input type="email" id="email" name="email" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
              </div>
              <div>
                <label for="subject" class="block text-sm font-medium text-gray-300 mb-1">Subject</label>
                <input type="text" id="subject" name="subject" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
              </div>
              <div>
                <label for="message" class="block text-sm font-medium text-gray-300 mb-1">Message</label>
                <textarea id="message" name="message" rows="4" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required></textarea>
              </div>
              <div>
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-all">Send Message</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="py-16 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12" data-aos="fade-up">
        <h2 class="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">Find answers to common questions about our healthcare software solutions.</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="bg-gray-800 p-6 rounded-lg shadow-md" data-aos="fade-up" data-aos-delay="100">
          <h3 class="text-xl font-bold mb-3">How do I schedule a demo?</h3>
          <p class="text-gray-300">You can schedule a demo by filling out the contact form on this page or by calling our sales team directly. We'll arrange a time that works for your schedule.</p>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg shadow-md" data-aos="fade-up" data-aos-delay="200">
          <h3 class="text-xl font-bold mb-3">What kind of support do you offer?</h3>
          <p class="text-gray-300">We provide 24/7 technical support for all our software solutions. Our support team is available via phone, email, and live chat to assist with any issues.</p>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg shadow-md" data-aos="fade-up" data-aos-delay="300">
          <h3 class="text-xl font-bold mb-3">Is your software HIPAA compliant?</h3>
          <p class="text-gray-300">Yes, all our healthcare software solutions are fully HIPAA compliant. We implement industry-leading security measures to protect patient data.</p>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg shadow-md" data-aos="fade-up" data-aos-delay="400">
          <h3 class="text-xl font-bold mb-3">Can your software integrate with our existing systems?</h3>
          <p class="text-gray-300">Our software is designed with interoperability in mind. We offer robust APIs and integration tools to connect with your existing healthcare systems.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
        </div>
        <div >
          <h3 class="text-lg font-bold mb-4">Products</h3>
          <ul class="space-y-2">
            <li><a href="product1.html" class="text-gray-300 hover:text-blue-400 transition-all">Dental Clinic Management</a></li>
            <li><a href="product2.html" class="text-gray-300 hover:text-blue-400 transition-all">Pharmacy Management</a></li>
            <li><a href="product3.html" class="text-gray-300 hover:text-blue-400 transition-all">Medical Clinic Management</a></li>
            <li><a href="product4.html" class="text-gray-300 hover:text-blue-400 transition-all">Patient-Centric App</a></li>
            <!-- Pricing link removed -->
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">Company</h3>
          <ul class="space-y-2">
            <li><a href="index.html#about" class="text-gray-300 hover:text-white">About Us</a></li>
            <li><a href="blog.html" class="text-gray-300 hover:text-white">Blog</a></li>
            <li><a href="contact.html" class="text-gray-300 hover:text-white">Contact</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">Contact</h3>
          <ul class="space-y-2">
            <li class="text-gray-300">United States</li>
            <li class="text-gray-300">+359 87 760 8877</li>
            <li class="text-gray-300"><EMAIL></li>
          </ul>
        </div>
      </div>
    </div>
  </footer>

  <!-- Initialize AOS and other scripts -->
  <script src="script.js"></script>
  <script>
    // Initialize AOS animation library
    document.addEventListener('DOMContentLoaded', function() {
      AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });

      // Mobile menu toggle
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');

      mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });

      // Form submission handling
      const contactForm = document.getElementById('contact-form');
      if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
          e.preventDefault();
          // Here you would typically send the form data to a server
          alert('Thank you for your message! We will get back to you soon.');
          contactForm.reset();
        });
      }
    });
  </script>
</body>
</html>

