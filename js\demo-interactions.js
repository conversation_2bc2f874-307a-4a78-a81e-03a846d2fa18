/**
 * CureOx Demo Interactions
 * Handles interactive product demonstrations and feature showcases
 */

class DemoInteractions {
  constructor() {
    this.initializeDemoNavigation();
    this.initializeHotspots();
    this.initializeAnimations();
    this.trackDemoEngagement();
  }

  /**
   * Initialize demo feature navigation
   */
  initializeDemoNavigation() {
    const featureButtons = document.querySelectorAll('.feature-button');
    const featureTitle = document.getElementById('feature-title');
    const featureDescription = document.getElementById('feature-description');

    if (featureButtons.length > 0 && featureTitle && featureDescription) {
      // Get feature descriptions from data attributes or use predefined ones
      const featureDescriptions = {};

      featureButtons.forEach(button => {
        const featureId = button.getAttribute('data-feature');
        const description = button.getAttribute('data-description');

        if (featureId && description) {
          featureDescriptions[featureId] = description;
        }
      });

      // Add click event listeners to feature buttons
      featureButtons.forEach(button => {
        button.addEventListener('click', function() {
          // Remove active class from all buttons
          featureButtons.forEach(btn => {
            btn.classList.remove('active');
            btn.classList.remove('bg-blue-600');
            btn.classList.add('bg-gray-700');
          });

          // Add active class to clicked button
          this.classList.add('active');
          this.classList.add('bg-blue-600');
          this.classList.remove('bg-gray-700');

          // Get feature ID
          const featureId = this.getAttribute('data-feature');

          // Hide all screens
          document.querySelectorAll('.demo-screen').forEach(screen => {
            screen.classList.remove('active');
          });

          // Show selected screen
          const selectedScreen = document.getElementById(`${featureId}-screen`);
          if (selectedScreen) {
            selectedScreen.classList.add('active');

            // Add entrance animation
            selectedScreen.style.animation = 'fadeIn 0.5s ease-in-out';
            setTimeout(() => {
              selectedScreen.style.animation = '';
            }, 500);
          }

          // Update feature title and description
          featureTitle.textContent = this.textContent.trim();

          if (featureDescriptions[featureId]) {
            featureDescription.textContent = featureDescriptions[featureId];
          }

          // Track feature interaction
          this.trackFeatureInteraction(featureId);
        });
      });
    }
  }

  /**
   * Initialize interactive hotspots
   */
  initializeHotspots() {
    const hotspots = document.querySelectorAll('.hotspot');

    hotspots.forEach(hotspot => {
      // Add hover effects
      hotspot.addEventListener('mouseenter', function() {
        // Removed transform scale to prevent movement
        this.style.backgroundColor = 'rgba(59, 130, 246, 0.9)';

        // Show tooltip
        const tooltip = this.nextElementSibling;
        if (tooltip && tooltip.classList.contains('tooltip')) {
          tooltip.style.opacity = '1';
        }
      });

      hotspot.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';

        // Hide tooltip
        const tooltip = this.nextElementSibling;
        if (tooltip && tooltip.classList.contains('tooltip')) {
          tooltip.style.opacity = '0';
        }
      });

      // Add click interaction
      hotspot.addEventListener('click', function() {
        // Get tooltip content
        const tooltip = this.nextElementSibling;
        const tooltipContent = tooltip ? tooltip.textContent : '';

        // Create a more detailed popup
        this.showDetailedPopup(tooltipContent, this);
      });
    });
  }

  /**
   * Show detailed popup for hotspot
   */
  showDetailedPopup(content, hotspot) {
    // Remove any existing popups
    const existingPopup = document.querySelector('.detailed-popup');
    if (existingPopup) {
      existingPopup.remove();
    }

    // Create popup element
    const popup = document.createElement('div');
    popup.className = 'detailed-popup fixed z-50 bg-gray-800 p-6 rounded-lg shadow-xl max-w-md';
    popup.style.top = '50%';
    popup.style.left = '50%';
    popup.style.transform = 'translate(-50%, -50%)';
    popup.style.border = '2px solid #3b82f6';
    popup.style.maxWidth = '90%';

    // Add content
    popup.innerHTML = `
      <div class="flex justify-between items-start mb-4">
        <h3 class="text-xl font-bold text-blue-400">Feature Detail</h3>
        <button class="close-popup text-gray-400 hover:text-white">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <p class="text-gray-300 mb-4">${content}</p>
      <div class="flex justify-end">
        <button class="close-popup bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Close</button>
      </div>
    `;

    // Add popup to the page
    document.body.appendChild(popup);

    // Add backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'popup-backdrop fixed inset-0 bg-black bg-opacity-50 z-40';
    document.body.appendChild(backdrop);

    // Add close functionality
    const closeButtons = popup.querySelectorAll('.close-popup');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        popup.remove();
        backdrop.remove();
      });
    });

    // Close on backdrop click
    backdrop.addEventListener('click', () => {
      popup.remove();
      backdrop.remove();
    });
  }

  /**
   * Initialize animations for demo elements
   */
  initializeAnimations() {
    // Add entrance animations to demo screens
    const demoScreens = document.querySelectorAll('.demo-screen');

    demoScreens.forEach(screen => {
      // Add CSS animation
      screen.style.transition = 'all 0.5s ease';

      // Add animation when screen becomes active
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          if (mutation.attributeName === 'class') {
            if (screen.classList.contains('active')) {
              // Trigger animation sequence for elements within the screen
              this.animateScreenElements(screen);
            }
          }
        });
      });

      observer.observe(screen, { attributes: true });
    });

    // Animate the initial active screen
    const activeScreen = document.querySelector('.demo-screen.active');
    if (activeScreen) {
      this.animateScreenElements(activeScreen);
    }
  }

  /**
   * Animate elements within a demo screen
   */
  animateScreenElements(screen) {
    // Animate hotspots with sequential delay
    const hotspots = screen.querySelectorAll('.hotspot');

    hotspots.forEach((hotspot, index) => {
      // Reset any existing animations
      hotspot.style.animation = 'none';
      hotspot.offsetHeight; // Trigger reflow

      // Add entrance animation with sequential delay - only fade in, no scaling
      hotspot.style.opacity = '0';

      setTimeout(() => {
        hotspot.style.opacity = '1';
        hotspot.style.transition = 'opacity 0.5s ease';
      }, 300 + (index * 150));
    });

    // Animate other elements if needed
    const animatableElements = screen.querySelectorAll('.animate-on-active');

    animatableElements.forEach((element, index) => {
      element.style.opacity = '0';
      element.style.transform = 'translateY(20px)';

      setTimeout(() => {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
        element.style.transition = 'all 0.5s ease';
      }, 500 + (index * 100));
    });
  }

  /**
   * Track demo engagement for personalization
   */
  trackDemoEngagement() {
    // Track which features the user interacts with
    const featureButtons = document.querySelectorAll('.feature-button');
    const demoContainer = document.querySelector('.demo-container');

    if (featureButtons.length > 0 && demoContainer) {
      // Get product type from page URL or container data attribute
      const productType = demoContainer.getAttribute('data-product') ||
                          window.location.pathname.includes('dentalpro') ? 'DentalPro' :
                          window.location.pathname.includes('pharmtrack') ? 'PharmTrack' :
                          window.location.pathname.includes('medoffice') ? 'MedOffice' : '';

      // Initialize engagement tracking object
      const engagement = {
        product: productType,
        featuresViewed: [],
        timeSpent: 0,
        hotspotClicks: 0
      };

      // Track feature button clicks
      featureButtons.forEach(button => {
        button.addEventListener('click', () => {
          const featureId = button.getAttribute('data-feature');

          if (featureId && !engagement.featuresViewed.includes(featureId)) {
            engagement.featuresViewed.push(featureId);
            this.updateEngagementData(engagement);
          }
        });
      });

      // Track hotspot clicks
      const hotspots = document.querySelectorAll('.hotspot');
      hotspots.forEach(hotspot => {
        hotspot.addEventListener('click', () => {
          engagement.hotspotClicks++;
          this.updateEngagementData(engagement);
        });
      });

      // Track time spent on demo
      let startTime = Date.now();
      let timeTracker = setInterval(() => {
        engagement.timeSpent = Math.floor((Date.now() - startTime) / 1000); // in seconds
        this.updateEngagementData(engagement);
      }, 5000); // Update every 5 seconds

      // Clear interval when user leaves page
      window.addEventListener('beforeunload', () => {
        clearInterval(timeTracker);

        // Final update before leaving
        engagement.timeSpent = Math.floor((Date.now() - startTime) / 1000);
        this.updateEngagementData(engagement);
      });
    }
  }

  /**
   * Update engagement data and use for personalization
   */
  updateEngagementData(engagement) {
    // Store engagement data in localStorage
    localStorage.setItem('demo_engagement', JSON.stringify(engagement));

    // Update user preferences if available
    if (window.userPreferences && engagement.product) {
      // Set preferred product based on significant engagement
      if (engagement.timeSpent > 60 || engagement.featuresViewed.length > 2) {
        window.userPreferences.incrementProductInterest(engagement.product);
        window.userPreferences.savePreferences();
      }
    }

    // Personalize demo experience based on engagement
    this.personalizeDemoExperience(engagement);
  }

  /**
   * Personalize demo experience based on user engagement
   */
  personalizeDemoExperience(engagement) {
    // If user has viewed multiple features, show a suggestion
    if (engagement.featuresViewed.length >= 2 && engagement.timeSpent > 30) {
      // Check if suggestion already exists
      if (!document.querySelector('.demo-suggestion')) {
        const suggestionElement = document.createElement('div');
        suggestionElement.className = 'demo-suggestion bg-blue-600 bg-opacity-20 border border-blue-500 text-blue-400 px-4 py-3 rounded mt-4 animate-fade-in';

        suggestionElement.innerHTML = `
          <p class="font-medium">Enjoying the demo?</p>
          <p class="text-sm mb-2">You might be interested in scheduling a personalized walkthrough with our team.</p>
          <a href="../demo.html?product=${engagement.product.toLowerCase()}" class="inline-block bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Request Full Demo</a>
        `;

        // Add suggestion to the page
        const demoContainer = document.querySelector('.demo-container').parentElement;
        demoContainer.appendChild(suggestionElement);
      }
    }

    // If user has clicked multiple hotspots, highlight remaining hotspots
    if (engagement.hotspotClicks >= 2) {
      const hotspots = document.querySelectorAll('.hotspot');
      hotspots.forEach(hotspot => {
        // Increase pulse animation intensity
        hotspot.style.animation = 'pulse 1.5s infinite';
      });
    }
  }
}

// Initialize demo interactions when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (document.querySelector('.demo-container')) {
    window.demoInteractions = new DemoInteractions();
  }
});
