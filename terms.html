<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Terms of Service - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="styles/theme.css" rel="stylesheet">
</head>
<body>
  <!-- Navigation -->
  <nav class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg fixed w-full z-10 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient flex items-center">
              <span class="mr-2">CureOx</span>
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </a>
          </div>
          <div class="hidden md:block ml-10">
            <div class="flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="product1.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management System</a>
              <a href="product2.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">PharmTrack</a>
              <a href="product3.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">MedOffice</a>
              <a href="pricing.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pricing</a>
            </div>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="flex items-center space-x-4">
            <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
            </button>
            <a href="contact.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-all">
              Contact Us
            </a>
          </div>
        </div>
        <div class="-mr-2 flex md:hidden">
          <button type="button" class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
      <div class="hidden mobile-menu md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
          <a href="product1.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management System</a>
          <a href="product2.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">PharmTrack</a>
          <a href="product3.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">MedOffice</a>
          <a href="pricing.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pricing</a>
          <div class="flex items-center justify-between px-3 py-2">
            <span class="text-gray-300">Dark/Light Mode</span>
            <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
          <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-blue-600 hover:bg-blue-700 mt-4">Contact Us</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Terms of Service Hero Section -->
  <section class="pt-32 pb-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">Terms of <span class="text-gradient">Service</span></h1>
        <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">These terms apply when you use our
          services. Read carefully before using the
          Cureox App or placing orders. By creating
          an account, you accept these terms.</p>
      </div>
    </div>
  </section>

  <!-- Terms of Service Content Section -->
  <section class="py-10 bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-gray-800 p-8 rounded-lg shadow-lg" data-aos="fade-up">
        <div class="prose prose-lg max-w-none text-gray-300">

          <h2 class="text-2xl font-bold mb-4 text-white">1. Purpose</h2>
          <p class="mb-6">We connect you with pharmacies and
            doctors to facilitate medicine orders and
            appointments, managing relationships
            with users, pharmacies, doctors, and
            delivery providers,</p>

          <h2 class="text-2xl font-bold mb-4 text-white">2. Your Account</h2>
          <p class="mb-6">Create an account with your phone
            number to order or book. You may close it
            anytime by deleting the app or contacting
            support. We may suspend/close accounts
            for misuse (e.g., fake orders,
            unreasonable complaints).</p>

          <h2 class="text-2xl font-bold mb-4 text-white">3. Service Availability</h2>
          <p class="mb-6">Pharmacies/doctors set service areas
            and hours, which may change due to
            demand or conditions (e.g., weather),
            We'll notify you if services are
            unavailable.</p>


          <h2 class="text-2xl font-bold mb-4 text-white">4. Orders and Appointments</h2>
          <p class="mb-6">Save orders as drafts or delete them
            before confirmation. Orders/
            appointments need pharmacy/doctor
            acceptance. You're responsible for full
            payment (items, delivery, or appointment
            fees),</p>

          <h2 class="text-2xl font-bold mb-4 text-white">5. Cancellation</h2>
          <p class="mb-6">Cancel free before confirmation or
            processing. Post-processing
            cancellations incur full item costs; post-
            dispatch includes delivery fees. We may
            cancel for uncontrollable reasons, with no
            charge.</p>

          <h2 class="text-2xl font-bold mb-4 text-white">6. Delivery</h2>
          <p class="mb-6">Choose ASAP or Scheduled Delivery. We
            provide estimated times and track orders.
            Delays over one hour (not your fault) may
            be free.</p>

          <h2 class="text-2xl font-bold mb-4 text-white">7. Addresses</h2>
          <p class="mb-6">Provide accurate addresses. Changes
            post-order may incur fees if processing/
            dispatch has begun</p>

          <h2 class="text-2xl font-bold mb-4 text-white">8. Fake Orders</h2>
          <p class="mb-6">Non-payment, unavailability, or address
            changes without fee payment may lead
            to account blocks and charges (50% of
            order + delivery fee).</p>

          <h2 class="text-2xl font-bold mb-4 text-white">9. Your Rights if Something ls
            Wrong</h2>
          <p class="mb-6">Refuse incorrect/damaged orders
            Provide photos if requested
            Compensation (refund/replacement)
            depends on issue severity,</p>

          <h2 class="text-2xl font-bold mb-4 text-white">10. E-Payment lssues</h2>
          <p class="mb-6">Refunds issued for rejected orders to the
            same account. No refunds for failed
            deliveries due to your error.</p>

          <h2 class="text-2xl font-bold mb-4 text-white">11.Prices and Payment</h2>
          <p class="mb-6">Dynamic pricing may apply. Pay via cash
            or e-payment. Delivery fees vary by
            distance.
            </p>

          <h2 class="text-2xl font-bold mb-4 text-white">12. Offers and Discounts</h2>
          <p class="mb-6">Pharmacy offers are time-limited
            Discount codes are single-use, valid for
            30 days</p>
        </div>
        <h2 class="text-2xl font-bold mb-4 text-white">13. Loyalty Program</h2>
        <p class="mb-6">Earn/redeem points (valid 90 days) for
          orders/appointments. Rewards are non-
          transferable.
          </p>
          <h2 class="text-2xl font-bold mb-4 text-white">14.E-Payment Terms</h2>
          <p class="mb-6">Use approved accounts. Orders can't be
            modified post-payment. Payment
            providers send confirmations.
            </p>
            <h2 class="text-2xl font-bold mb-4 text-white">15. Service and Platform Updates</h2>
            <p class="mb-6">We may update or suspend services/
              content without notice.
              </p>
              <h2 class="text-2xl font-bold mb-4 text-white">16. Complaints and Suggestions</h2>
              <p class="mb-6">Contact <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-blue-400 transition-all"><EMAIL> </a>to help
                us improve <br>


                Contact Information
                <br>

                <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-blue-400 transition-all"><EMAIL></a><br>
                Address: United States
                </p>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mt-8">
        <!-- Products Column -->
        <div >
          <h3 class="text-lg font-bold mb-4">Products</h3>
          <ul class="space-y-2">
            <li><a href="product1.html" class="text-gray-300 hover:text-blue-400 transition-all">Dental Clinic Management</a></li>
            <li><a href="product2.html" class="text-gray-300 hover:text-blue-400 transition-all">Pharmacy Management</a></li>
            <li><a href="product3.html" class="text-gray-300 hover:text-blue-400 transition-all">Medical Clinic Management</a></li>
            <li><a href="product4.html" class="text-gray-300 hover:text-blue-400 transition-all">Patient-Centric App</a></li>
            <!-- Pricing link removed -->
          </ul>
        </div>

        <!-- Company Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Company</h3>
          <ul class="space-y-2">
            <li><a href="index.html#about" class="text-gray-300 hover:text-blue-400 transition-all">About Us</a></li>
            <li><a href="index.html#services" class="text-gray-300 hover:text-blue-400 transition-all">Services</a></li>
            <li><a href="contact.html" class="text-gray-300 hover:text-blue-400 transition-all">Contact</a></li>
          </ul>
        </div>

        <!-- Resources Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Resources</h3>
          <ul class="space-y-2">
            <li><a href="index.html#faq" class="text-gray-300 hover:text-blue-400 transition-all">FAQ</a></li>
            <li><a href="blog.html" class="text-gray-300 hover:text-blue-400 transition-all">Blog</a></li>
            <li><a href="terms.html" class="text-gray-300 hover:text-blue-400 transition-all">Terms of Service</a></li>
            <li><a href="privacy.html" class="text-gray-300 hover:text-blue-400 transition-all">Privacy Policy</a></li>
          </ul>
        </div>

        <!-- Contact Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Contact</h3>
          <ul class="space-y-2">
            <li class="text-gray-300">United States</li>
            <li class="text-gray-300">+359 87 7608877</li>
            <li><a href="mailto:<EMAIL>" class="text-gray-300 hover:text-blue-400 transition-all"><EMAIL></a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2025 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="script.js"></script>
</body>
</html>

