<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Support - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <link href="styles/theme.css" rel="stylesheet">
</head>
<body>
  <!-- Navbar -->
  <nav class="fixed w-full z-50 backdrop-filter backdrop-blur-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          </div>
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="index.html#about" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">About Us</a>
              <a href="index.html#services" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Services</a>
              <a href="index.html#products" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Products</a>
              <a href="blog.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Blog</a>
              <a href="contact.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Contact</a>
              <a href="support.html" class="px-3 py-2 rounded-md text-sm font-medium bg-blue-700 text-white transition-all">Support</a>
            </div>
          </div>
        </div>
        <div class="hidden md:flex items-center ml-4">
          <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-700 focus:outline-none transition-colors">
            <svg id="moon-icon" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
            <svg id="sun-icon" class="h-6 w-6 text-gray-300 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </button>
        </div>
        <div class="hidden md:flex items-center">
          <a href="contact.html" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Contact Us</a>
        </div>
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button id="mobile-menu-button" class="text-gray-400 hover:text-white focus:outline-none">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Home</a>
        <a href="index.html#about" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">About Us</a>
        <a href="index.html#services" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Services</a>
        <a href="index.html#products" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Products</a>
        <a href="blog.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Blog</a>
        <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Contact</a>
        <a href="support.html" class="block px-3 py-2 rounded-md text-base font-medium bg-blue-700 text-white">Support</a>
        <div class="mt-4 md:hidden">
          <button id="mobile-theme-toggle" class="p-2 rounded-full hover:bg-gray-700 focus:outline-none transition-colors">
            <svg id="mobile-moon-icon" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
            <svg id="mobile-sun-icon" class="h-6 w-6 text-gray-300 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Support Header -->
  <section class="pt-32 pb-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">Customer <span class="text-gradient">Support</span></h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">We're committed to providing exceptional support for all our healthcare software solutions.</p>
      </div>
    </div>
  </section>

  <!-- Support Options -->
  <section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Technical Support -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-8" data-aos="fade-up" data-aos-delay="100">
          <div class="text-blue-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-3">Technical Support</h3>
          <p class="text-gray-300 mb-4">Get help with technical issues, software bugs, or system configuration problems.</p>
          <div class="mt-6">
            <p class="text-gray-300"><strong>Phone:</strong> (*************</p>
            <p class="text-gray-300"><strong>Email:</strong> <EMAIL></p>
          </div>
        </div>
        
        <!-- Add missing content blocks here -->
      </div>
    </div>
  </section>

  <!-- Support Resources -->
  <section class="py-16 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12" data-aos="fade-up">
        <h2 class="text-3xl font-bold mb-4">Support <span class="text-gradient">Resources</span></h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">Access our comprehensive knowledge base and resources to help you get the most out of our software.</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Knowledge Base -->
        <div class="bg-gray-800 p-8 rounded-lg shadow-lg" data-aos="fade-up" data-aos-delay="100">
          <h3 class="text-xl font-bold mb-4">Knowledge Base</h3>
          <p class="text-gray-300 mb-6">Browse our extensive collection of articles, tutorials, and guides to help you navigate our software solutions.</p>
          <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Browse Knowledge Base
            <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>
        
        <!-- Video Tutorials -->
        <div class="bg-gray-800 p-8 rounded-lg shadow-lg" data-aos="fade-up" data-aos-delay="200">
          <h3 class="text-xl font-bold mb-4">Video Tutorials</h3>
          <p class="text-gray-300 mb-6">Watch step-by-step video tutorials to learn how to use all the features of our healthcare software.</p>
          <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Watch Tutorials
            <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Support Ticket -->
  <section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div data-aos="fade-right">
          <h2 class="text-3xl font-bold mb-6">Submit a <span class="text-gradient">Support Ticket</span></h2>
          <p class="text-xl text-gray-300 mb-8">Need help with a specific issue? Submit a support ticket and our team will respond promptly.</p>
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-12 w-12 bg-blue-600 rounded-md flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium">Fast Response Times</h3>
                <p class="text-gray-300">We aim to respond to all support tickets within 2 hours during business hours.</p>
              </div>
            </div>
            <!-- Add form here -->
          </div>
        </div>
        
        <!-- Support Ticket Form -->
        <div class="bg-gray-800 p-8 rounded-lg shadow-lg" data-aos="fade-left">
          <h2 class="text-2xl font-bold mb-6">Support Ticket Form</h2>
          <form id="support-form" action="#" method="POST">
            <div class="grid grid-cols-1 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Your Name</label>
                <input type="text" id="name" name="name" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email Address</label>
                <input type="email" id="email" name="email" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
              </div>
              <div>
                <label for="product" class="block text-sm font-medium text-gray-300 mb-1">Product</label>
                <select id="product" name="product" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
                  <option value="">Select a product</option>
                  <option value="DentalPro">DentalPro</option>
                  <option value="PharmTrack">PharmTrack</option>
                  <option value="MedOffice">MedOffice</option>
                </select>
              </div>
              <div>
                <label for="issue-type" class="block text-sm font-medium text-gray-300 mb-1">Issue Type</label>
                <select id="issue-type" name="issue-type" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required>
                  <option value="">Select issue type</option>
                  <option value="Technical">Technical Issue</option>
                  <option value="Billing">Billing Question</option>
                  <option value="Feature">Feature Request</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-300 mb-1">Issue Description</label>
                <textarea id="description" name="description" rows="4" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white" required></textarea>
              </div>
              <div>
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-all">Submit Ticket</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Form submission handling
      const supportForm = document.getElementById('support-form');
      if (supportForm) {
        supportForm.addEventListener('submit', function(e) {
          e.preventDefault();
          // Here you would typically send the form data to a server
          alert('Thank you for submitting a support ticket! Our team will contact you shortly.');
          supportForm.reset();
        });
      }

      // Theme toggle functionality
      const themeToggle = document.getElementById('theme-toggle');
      const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
      const moonIcon = document.getElementById('moon-icon');
      const sunIcon = document.getElementById('sun-icon');
      const mobileMoonIcon = document.getElementById('mobile-moon-icon');
      const mobileSunIcon = document.getElementById('mobile-sun-icon');
      
      // Check for saved theme preference or use dark mode by default
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'light') {
        document.documentElement.classList.add('light-mode');
        document.documentElement.classList.remove('dark-mode');
        if (moonIcon && sunIcon) {
          moonIcon.classList.add('hidden');
          sunIcon.classList.remove('hidden');
        }
        if (mobileMoonIcon && mobileSunIcon) {
          mobileMoonIcon.classList.add('hidden');
          mobileSunIcon.classList.remove('hidden');
        }
      } else {
        document.documentElement.classList.add('dark-mode');
        document.documentElement.classList.remove('light-mode');
        if (moonIcon && sunIcon) {
          moonIcon.classList.remove('hidden');
          sunIcon.classList.add('hidden');
        }
        if (mobileMoonIcon && mobileSunIcon) {
          mobileMoonIcon.classList.remove('hidden');
          mobileSunIcon.classList.add('hidden');
        }
      }
      
      // Toggle theme function
      function toggleTheme() {
        if (document.documentElement.classList.contains('light-mode')) {
          document.documentElement.classList.remove('light-mode');
          document.documentElement.classList.add('dark-mode');
          if (moonIcon && sunIcon) {
            moonIcon.classList.remove('hidden');
            sunIcon.classList.add('hidden');
          }
          if (mobileMoonIcon && mobileSunIcon) {
            mobileMoonIcon.classList.remove('hidden');
            mobileSunIcon.classList.add('hidden');
          }
          localStorage.setItem('theme', 'dark');
        } else {
          document.documentElement.classList.remove('dark-mode');
          document.documentElement.classList.add('light-mode');
          if (moonIcon && sunIcon) {
            moonIcon.classList.add('hidden');
            sunIcon.classList.remove('hidden');
          }
          if (mobileMoonIcon && mobileSunIcon) {
            mobileMoonIcon.classList.add('hidden');
            mobileSunIcon.classList.remove('hidden');
          }
          localStorage.setItem('theme', 'light');
        }
      }
      
      // Add event listeners to theme toggles
      if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
      }
      
      if (mobileThemeToggle) {
        mobileThemeToggle.addEventListener('click', toggleTheme);
      }
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize AOS
      AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
      
      // Mobile menu toggle
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      
      if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
          mobileMenu.classList.toggle('hidden');
        });
      }
    });
  </script>
</body>
</html>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="md:col-span-1">
          <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
          <div class="flex space-x-4 mt-6">
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16.98 0a6.9 6.9 0 0 1 5.08 1.98A6.94 6.94 0 0 1 24 7.02v9.96c0 2.08-.68 3.87-1.98 5.13A7.14 7.14 0 0 1 16.94 24H7.06a7.06 7.06 0 0 1-5.03-1.89A6.96 6.96 0 0 1 0 16.94V7.02C0 2.8 2.8 0 7.02 0h9.96zm.05 2.23H7.06c-1.45 0-2.7.43-3.53 1.25a4.82 4.82 0 0 0-1.3 3.54v9.92c0 1.5.43 2.7 1.3 3.58a5 5 0 0 0 3.53 1.25h9.88a5 5 0 0 0 3.53-1.25 4.73 4.73 0 0 0 1.4-3.54V7.02a5 5 0 0 0-1.3-3.49 4.82 4.82 0 0 0-3.54-1.3zM12 5.76c3.39 0 6.2 2.8 6.2 6.2a6.2 6.2 0 0 1-12.4 0 6.2 6.2 0 0 1 6.2-6.2zm0 2.22a3.99 3.99 0 0 0-3.97 3.97A3.99 3.99 0 0 0 12 15.92a3.99 3.99 0 0 0 3.97-3.97A3.99 3.99 0 0 0 12 7.98z"></path>
              </svg>
            </a>
          </div>
        </div>
        
        <!-- Products -->
        <div>
          <h3 class="text-lg font-bold mb-4">Products</h3>
          <ul class="space-y-2">
            <li><a href="product1.html" class="text-gray-300 hover:text-white">DentalPro</a></li>
            <li><a href="product2.html" class="text-gray-300 hover:text-white">PharmTrack</a></li>
            <li><a href="product3.html" class="text-gray-300 hover:text-white">MedOffice</a></li>
          </ul>
        </div>
        
        <!-- Quick Links -->
        <div>
          <h3 class="text-lg font-bold mb-4">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="index.html" class="text-gray-300 hover:text-white">Home</a></li>
            <li><a href="blog.html" class="text-gray-300 hover:text-white">Blog</a></li>
            <li><a href="pricing.html" class="text-gray-300 hover:text-white">Pricing</a></li>
            <li><a href="demo.html" class="text-gray-300 hover:text-white">Request Demo</a></li>
          </ul>
        </div>
        
        <!-- Contact -->
        <div>
          <h3 class="text-lg font-bold mb-4">Contact</h3>
          <div class="space-y-3">
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <p class="text-gray-300">123 Healthcare Ave, Suite 500<br>San Francisco, CA 94107</p>
            </div>
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white"><EMAIL></a>
            </div>
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              <p class="text-gray-300">(*************</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>



