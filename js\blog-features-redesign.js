/**
 * CureOx Blog Features - Redesigned
 * Enhanced blog functionality for the redesigned blog page
 */

class BlogFeaturesRedesign {
  constructor() {
    this.initializeSearch();
    this.initializeFilters();
    this.initializeLoadMore();
    this.initializeTagCloud();
    this.initializeNewsletterSubscription();
    this.initializeArticleCards();
  }

  /**
   * Initialize enhanced blog search functionality
   */
  initializeSearch() {
    const searchInput = document.getElementById('blog-search-input');
    const searchResults = document.getElementById('blog-search-results');
    const searchButton = document.getElementById('search-button');
    
    if (!searchInput || !searchResults) return;
    
    // Show search results when typing
    searchInput.addEventListener('input', (e) => {
      const query = e.target.value.toLowerCase().trim();
      
      if (query.length < 2) {
        searchResults.classList.add('hidden');
        return;
      }
      
      // Get all blog posts
      const blogPosts = document.querySelectorAll('.blog-card');
      const matchingPosts = [];
      
      blogPosts.forEach(post => {
        const title = post.querySelector('h3').textContent.toLowerCase();
        const content = post.querySelector('p').textContent.toLowerCase();
        const author = post.querySelector('.text-sm.text-gray-400').textContent.toLowerCase();
        const category = post.dataset.category.toLowerCase();
        
        if (title.includes(query) || content.includes(query) || author.includes(query) || category.includes(query)) {
          matchingPosts.push({
            title: post.querySelector('h3').textContent,
            author: post.querySelector('.text-sm.text-gray-400').textContent.split('•')[0].trim(),
            category: post.dataset.category,
            link: post.querySelector('a').getAttribute('href')
          });
        }
      });
      
      // Display results
      if (matchingPosts.length > 0) {
        searchResults.innerHTML = '';
        searchResults.classList.remove('hidden');
        
        matchingPosts.forEach(post => {
          const resultItem = document.createElement('div');
          resultItem.className = 'p-4 border-b border-gray-700 hover:bg-gray-700 transition-colors';
          resultItem.innerHTML = `
            <a href="${post.link}" class="block">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="font-medium text-white">${post.title}</h4>
                  <p class="text-sm text-gray-400">${post.author}</p>
                </div>
                <span class="bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-2 py-1 rounded">${post.category}</span>
              </div>
            </a>
          `;
          searchResults.appendChild(resultItem);
        });
      } else {
        searchResults.innerHTML = `
          <div class="p-4 text-center text-gray-400">
            No results found for "${query}"
          </div>
        `;
        searchResults.classList.remove('hidden');
      }
    });
    
    // Hide search results when clicking outside
    document.addEventListener('click', (e) => {
      if (!searchInput.contains(e.target) && !searchResults.contains(e.target) && !searchButton.contains(e.target)) {
        searchResults.classList.add('hidden');
      }
    });
    
    // Focus search input when clicking the search button
    searchButton.addEventListener('click', () => {
      searchInput.focus();
    });
  }

  /**
   * Initialize category filtering
   */
  initializeFilters() {
    const categoryButtons = document.querySelectorAll('.category-pill');
    const blogCards = document.querySelectorAll('.blog-card');
    
    if (!categoryButtons.length || !blogCards.length) return;
    
    categoryButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Update active state
        categoryButtons.forEach(btn => btn.classList.remove('active', 'bg-blue-600', 'text-white'));
        button.classList.add('active', 'bg-blue-600', 'text-white');
        
        const category = button.dataset.category;
        
        // Filter blog posts
        blogCards.forEach(card => {
          if (category === 'all' || card.dataset.category === category) {
            card.parentElement.classList.remove('hidden');
            // Add animation
            card.classList.add('animate-fade-in');
            setTimeout(() => {
              card.classList.remove('animate-fade-in');
            }, 500);
          } else {
            card.parentElement.classList.add('hidden');
          }
        });
      });
    });
  }

  /**
   * Initialize load more functionality
   */
  initializeLoadMore() {
    const loadMoreButton = document.getElementById('load-more');
    if (!loadMoreButton) return;
    
    // In a real implementation, this would load more articles from the server
    // For demo purposes, we'll just show a message
    loadMoreButton.addEventListener('click', () => {
      loadMoreButton.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading...
      `;
      
      // Simulate loading delay
      setTimeout(() => {
        loadMoreButton.innerHTML = `
          No more articles to load
        `;
        loadMoreButton.disabled = true;
        loadMoreButton.classList.add('bg-gray-600');
        loadMoreButton.classList.remove('hover:bg-blue-700', 'bg-blue-600');
      }, 1500);
    });
  }

  /**
   * Initialize tag cloud functionality
   */
  initializeTagCloud() {
    const tagItems = document.querySelectorAll('.tag-cloud span');
    if (!tagItems.length) return;
    
    tagItems.forEach(tag => {
      tag.addEventListener('click', () => {
        // In a real implementation, this would filter articles by tag
        // For demo purposes, we'll just highlight the selected tag
        tagItems.forEach(t => t.classList.remove('bg-blue-600', 'text-white'));
        tag.classList.add('bg-blue-600', 'text-white');
        
        // Show notification
        this.showNotification(`Filtering by: ${tag.textContent}`);
      });
    });
  }

  /**
   * Initialize newsletter subscription
   */
  initializeNewsletterSubscription() {
    const newsletterForm = document.getElementById('newsletter-form');
    if (!newsletterForm) return;
    
    newsletterForm.addEventListener('submit', (e) => {
      e.preventDefault();
      
      const emailInput = document.getElementById('newsletter-email');
      const email = emailInput.value.trim();
      
      if (!email || !this.isValidEmail(email)) {
        this.showNotification('Please enter a valid email address', 'error');
        return;
      }
      
      // Get selected interests
      const interests = [];
      document.querySelectorAll('.newsletter-interest:checked').forEach(checkbox => {
        interests.push(checkbox.value);
      });
      
      // In a real implementation, this would submit the form to a server
      // For demo purposes, we'll just show a success message
      
      // Show loading state
      const originalButtonText = newsletterForm.querySelector('button[type="submit"]').innerHTML;
      newsletterForm.querySelector('button[type="submit"]').innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Subscribing...
      `;
      
      // Simulate form submission
      setTimeout(() => {
        // Replace form with success message
        newsletterForm.innerHTML = `
          <div class="bg-green-900 bg-opacity-20 border border-green-500 text-green-400 p-6 rounded-lg text-center">
            <svg class="w-16 h-16 mx-auto mb-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="text-xl font-bold mb-2">Successfully Subscribed!</h3>
            <p class="mb-4">Thank you for subscribing to our newsletter. You'll receive personalized content based on your interests.</p>
            <p class="text-sm">We've sent a confirmation email to <strong>${email}</strong></p>
          </div>
        `;
        
        // Save preferences in user preferences system
        if (window.userPreferenceTracker) {
          interests.forEach(interest => {
            if (!window.userPreferenceTracker.preferences.blogCategories.includes(interest)) {
              window.userPreferenceTracker.preferences.blogCategories.push(interest);
            }
          });
          window.userPreferenceTracker.savePreferences();
        }
      }, 1500);
    });
  }

  /**
   * Initialize article card hover effects and interactions
   */
  initializeArticleCards() {
    const articleCards = document.querySelectorAll('.blog-card');
    if (!articleCards.length) return;
    
    articleCards.forEach(card => {
      // Add hover effect
      card.addEventListener('mouseenter', () => {
        card.classList.add('transform', 'scale-102');
      });
      
      card.addEventListener('mouseleave', () => {
        card.classList.remove('transform', 'scale-102');
      });
    });
  }

  /**
   * Show notification message
   * @param {string} message - Message to display
   * @param {string} type - Type of notification (success, error, info)
   */
  showNotification(message, type = 'info') {
    // Create notification element if it doesn't exist
    let notification = document.getElementById('blog-notification');
    
    if (!notification) {
      notification = document.createElement('div');
      notification.id = 'blog-notification';
      notification.className = 'fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-y-20 opacity-0';
      document.body.appendChild(notification);
    }
    
    // Set notification type
    notification.className = 'fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-y-20 opacity-0';
    
    if (type === 'success') {
      notification.classList.add('bg-green-800', 'text-green-200');
    } else if (type === 'error') {
      notification.classList.add('bg-red-800', 'text-red-200');
    } else {
      notification.classList.add('bg-blue-800', 'text-blue-200');
    }
    
    // Set message
    notification.textContent = message;
    
    // Show notification
    setTimeout(() => {
      notification.classList.remove('translate-y-20', 'opacity-0');
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
      notification.classList.add('translate-y-20', 'opacity-0');
    }, 3000);
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} - Whether email is valid
   */
  isValidEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email.toLowerCase());
  }
}

// Initialize blog features when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.blogFeaturesRedesign = new BlogFeaturesRedesign();
});
