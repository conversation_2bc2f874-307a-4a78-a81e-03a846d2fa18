<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Checkout - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="styles/theme.css" rel="stylesheet">
  <style>
    .steady-card {
      transform: none !important;
      transition: none !important;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg fixed w-full z-10 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient flex items-center">
              <span class="mr-2">CureOx</span>
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </a>
          </div>
          <div class="hidden md:block ml-10">
            <div class="flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="product1.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management</a>
              <a href="product2.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management</a>
              <a href="product3.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management</a>
              <a href="product4.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
              <a href="pricing.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pricing</a>
            </div>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="flex items-center space-x-4">
            <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
            </button>
            <a href="contact.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-all">
              Contact Us
            </a>
          </div>
        </div>
        <div class="-mr-2 flex md:hidden">
          <button type="button" class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
      <div class="hidden mobile-menu md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
          <a href="product1.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management</a>
          <a href="product2.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management</a>
          <a href="product3.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management</a>
          <a href="product4.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
          <a href="pricing.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pricing</a>
          <div class="flex items-center justify-between px-3 py-2">
            <span class="text-gray-300">Dark/Light Mode</span>
            <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
          <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-blue-600 hover:bg-blue-700 mt-4">Contact Us</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Checkout Hero Section -->
  <section class="pt-32 pb-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">Complete Your <span class="text-gradient">Purchase</span></h1>
        <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">You're just a few steps away from transforming your healthcare practice</p>
      </div>
    </div>
  </section>

  <!-- Checkout Section -->
  <section class="py-10 bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Checkout Form Column -->
        <div class="lg:col-span-2">
          <div class="bg-gray-800 p-8 rounded-lg shadow-lg steady-card">
            <h2 class="text-2xl font-bold mb-6">Billing Information</h2>
            <form id="checkout-form" class="space-y-6">
              <!-- Personal Information -->
              <div>
                <h3 class="text-lg font-semibold mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="first-name" class="block text-sm font-medium text-gray-300 mb-1">First Name *</label>
                    <input type="text" id="first-name" name="first-name" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                  <div>
                    <label for="last-name" class="block text-sm font-medium text-gray-300 mb-1">Last Name *</label>
                    <input type="text" id="last-name" name="last-name" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                </div>
                <div class="mt-4">
                  <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email Address *</label>
                  <input type="email" id="email" name="email" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mt-4">
                  <label for="phone" class="block text-sm font-medium text-gray-300 mb-1">Phone Number *</label>
                  <input type="tel" id="phone" name="phone" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
              </div>

              <!-- Company Information -->
              <div>
                <h3 class="text-lg font-semibold mb-4">Company Information</h3>
                <div>
                  <label for="company" class="block text-sm font-medium text-gray-300 mb-1">Company Name *</label>
                  <input type="text" id="company" name="company" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mt-4">
                  <label for="address" class="block text-sm font-medium text-gray-300 mb-1">Address *</label>
                  <input type="text" id="address" name="address" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div>
                    <label for="city" class="block text-sm font-medium text-gray-300 mb-1">City *</label>
                    <input type="text" id="city" name="city" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                  <div>
                    <label for="state" class="block text-sm font-medium text-gray-300 mb-1">State/Province *</label>
                    <input type="text" id="state" name="state" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                  <div>
                    <label for="zip" class="block text-sm font-medium text-gray-300 mb-1">ZIP/Postal Code *</label>
                    <input type="text" id="zip" name="zip" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                </div>
                <div class="mt-4">
                  <label for="country" class="block text-sm font-medium text-gray-300 mb-1">Country *</label>
                  <select id="country" name="country" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Select a country</option>
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="UK">United Kingdom</option>
                    <option value="AU">Australia</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
              </div>

              <!-- Payment Information -->
              <div>
                <h3 class="text-lg font-semibold mb-4">Payment Information</h3>
                <div>
                  <label for="card-name" class="block text-sm font-medium text-gray-300 mb-1">Name on Card *</label>
                  <input type="text" id="card-name" name="card-name" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mt-4">
                  <label for="card-number" class="block text-sm font-medium text-gray-300 mb-1">Card Number *</label>
                  <input type="text" id="card-number" name="card-number" placeholder="XXXX XXXX XXXX XXXX" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div class="col-span-1">
                    <label for="expiry-month" class="block text-sm font-medium text-gray-300 mb-1">Expiry Month *</label>
                    <select id="expiry-month" name="expiry-month" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option value="">MM</option>
                      <option value="01">01</option>
                      <option value="02">02</option>
                      <option value="03">03</option>
                      <option value="04">04</option>
                      <option value="05">05</option>
                      <option value="06">06</option>
                      <option value="07">07</option>
                      <option value="08">08</option>
                      <option value="09">09</option>
                      <option value="10">10</option>
                      <option value="11">11</option>
                      <option value="12">12</option>
                    </select>
                  </div>
                  <div class="col-span-1">
                    <label for="expiry-year" class="block text-sm font-medium text-gray-300 mb-1">Expiry Year *</label>
                    <select id="expiry-year" name="expiry-year" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option value="">YYYY</option>
                      <option value="2023">2023</option>
                      <option value="2024">2024</option>
                      <option value="2025">2025</option>
                      <option value="2026">2026</option>
                      <option value="2027">2027</option>
                      <option value="2028">2028</option>
                      <option value="2029">2029</option>
                      <option value="2030">2030</option>
                    </select>
                  </div>
                  <div class="col-span-1">
                    <label for="cvv" class="block text-sm font-medium text-gray-300 mb-1">CVV *</label>
                    <input type="text" id="cvv" name="cvv" placeholder="XXX" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                </div>
              </div>

              <div class="pt-4">
                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-all">Complete Purchase</button>
              </div>
            </form>
          </div>
        </div>

        <!-- Order Summary Column -->
        <div class="lg:col-span-1">
          <div class="bg-gray-800 p-8 rounded-lg shadow-lg sticky top-24 steady-card">
            <h2 class="text-2xl font-bold mb-6">Order Summary</h2>
            <div id="product-details" class="mb-6">
              <!-- Product details will be populated by JavaScript -->
              <div class="skeleton-loader h-24 bg-gray-700 animate-pulse rounded-md mb-4"></div>
            </div>
            <div class="border-t border-gray-700 pt-4 mb-4">
              <div class="flex justify-between mb-2">
                <span class="text-gray-300">Subtotal</span>
                <span id="subtotal" class="font-semibold">$0.00</span>
              </div>
              <div class="flex justify-between mb-2">
                <span class="text-gray-300">Tax</span>
                <span id="tax" class="font-semibold">$0.00</span>
              </div>
            </div>
            <div class="border-t border-gray-700 pt-4">
              <div class="flex justify-between mb-2">
                <span class="text-xl font-bold">Total</span>
                <span id="total" class="text-xl font-bold">$0.00</span>
              </div>
              <div class="text-sm text-gray-400 mt-2">
                <p>Billed monthly. Cancel anytime.</p>
                <div class="text-sm text-gray-400 mt-6 terms-privacy">
                  By completing your purchase, you agree to our
                  <a href="terms.html" class="text-blue-400 hover:text-blue-300">Terms of Service</a> and
                  <a href="privacy.html" class="text-blue-400 hover:text-blue-300">Privacy Policy</a>.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mt-8">
        <!-- Products Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Products</h3>
          <ul class="space-y-2">
            <li><a href="product1.html" class="text-gray-300 hover:text-blue-400 transition-all">Dental Clinic Management</a></li>
            <li><a href="product2.html" class="text-gray-300 hover:text-blue-400 transition-all">Pharmacy Management</a></li>
            <li><a href="product3.html" class="text-gray-300 hover:text-blue-400 transition-all">Medical Clinic Management</a></li>
            <li><a href="product4.html" class="text-gray-300 hover:text-blue-400 transition-all">Patient-Centric App</a></li>
            <!-- Pricing link removed -->
          </ul>
        </div>

        <!-- Company Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Company</h3>
          <ul class="space-y-2">
            <li><a href="index.html#about" class="text-gray-300 hover:text-blue-400 transition-all">About Us</a></li>
            <li><a href="index.html#services" class="text-gray-300 hover:text-blue-400 transition-all">Services</a></li>
            <li><a href="contact.html" class="text-gray-300 hover:text-blue-400 transition-all">Contact</a></li>
          </ul>
        </div>

        <!-- Resources Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Resources</h3>
          <ul class="space-y-2">
            <li><a href="index.html#faq" class="text-gray-300 hover:text-blue-400 transition-all">FAQ</a></li>
            <li><a href="blog.html" class="text-gray-300 hover:text-blue-400 transition-all">Blog</a></li>
            <li><a href="terms.html" class="text-gray-300 hover:text-blue-400 transition-all">Terms of Service</a></li>
            <li><a href="privacy.html" class="text-gray-300 hover:text-blue-400 transition-all">Privacy Policy</a></li>
          </ul>
        </div>

        <!-- Contact Column -->
        <div>
          <h3 class="text-lg font-bold mb-4">Contact</h3>
          <ul class="space-y-2">
            <li class="text-gray-300">123 Healthcare Ave</li>
            <li class="text-gray-300">Suite 456</li>
            <li class="text-gray-300">San Francisco, CA 94107</li>
            <li><a href="mailto:<EMAIL>" class="text-gray-300 hover:text-blue-400 transition-all"><EMAIL></a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="script.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize AOS
      AOS.init();

      // Product data
      const products = {
        'dentalpro-basic': {
          name: 'Dental Clinic Management Basic',
          description: 'Essential practice management for dental clinics',
          price: 49,
          image: 'images/Dentist.jpg'
        },
        'dentalpro-premium': {
          name: 'Dental Clinic Management Premium',
          description: 'Advanced dental practice management solution',
          price: 79,
          image: 'images/Dentist.jpg'
        },
        'pharmtrack-standard': {
          name: 'Pharmacy Management Standard',
          description: 'Basic pharmacy management system',
          price: 39,
          image: 'images/Pharmacy.png'
        },
        'pharmtrack-enterprise': {
          name: 'Pharmacy Management Enterprise',
          description: 'Comprehensive pharmacy management solution',
          price: 69,
          image: 'images/Pharmacy.png'
        },
        'medoffice-basic': {
          name: 'Medical Clinic Management Basic',
          description: 'Essential medical office management',
          price: 59,
          image: 'images/Doctor.png'
        },
        'medoffice-pro': {
          name: 'Medical Clinic Management Pro',
          description: 'Complete medical practice management solution',
          price: 89,
          image: 'images/Doctor.png'
        },
        'patient-app-basic': {
          name: 'Patient-Centric Basic',
          description: 'Essential patient engagement tools',
          price: 29,
          image: 'images/HeroSection.jpg'
        },
        'patient-app-premium': {
          name: 'Patient-Centric Premium',
          description: 'Complete patient engagement platform',
          price: 49,
          image: 'images/HeroSection.jpg'
        }
      };

      // Get product from URL query parameter
      const urlParams = new URLSearchParams(window.location.search);
      const productId = urlParams.get('product');

      // Display product details
      const productDetailsElement = document.getElementById('product-details');
      const subtotalElement = document.getElementById('subtotal');
      const taxElement = document.getElementById('tax');
      const totalElement = document.getElementById('total');

      if (productId && products[productId]) {
        const product = products[productId];
        const tax = product.price * 0.08; // 8% tax rate
        const total = product.price + tax;

        // Update product details
        productDetailsElement.innerHTML = `
          <div class="flex items-center mb-4">
            <img src="${product.image}" alt="${product.name}" class="w-16 h-16 object-cover rounded-md mr-4">
            <div>
              <h3 class="font-bold">${product.name}</h3>
              <p class="text-gray-300 text-sm">${product.description}</p>
            </div>
          </div>
          <div class="flex justify-between mb-2">
            <span class="text-gray-300">Monthly Subscription</span>
            <span class="font-semibold">$${product.price}.00</span>
          </div>
        `;

        // Update pricing
        subtotalElement.textContent = `$${product.price}.00`;
        taxElement.textContent = `$${tax.toFixed(2)}`;
        totalElement.textContent = `$${total.toFixed(2)}`;
      } else {
        // No product selected or invalid product
        productDetailsElement.innerHTML = `
          <div class="bg-red-500 bg-opacity-20 border border-red-400 text-red-300 px-4 py-3 rounded">
            <p class="font-medium">No product selected</p>
            <p class="text-sm">Please return to the <a href="pricing.html" class="underline">pricing page</a> to select a product.</p>
          </div>
        `;

        // Update pricing to zero
        subtotalElement.textContent = '$0.00';
        taxElement.textContent = '$0.00';
        totalElement.textContent = '$0.00';
      }

      // Handle form submission
      const checkoutForm = document.getElementById('checkout-form');
      if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
          e.preventDefault();

          // In a real implementation, you would send the form data to a server for processing
          // For this demo, we'll just show a success message
          alert('Thank you for your purchase! Your order has been processed successfully.');

          // Redirect to a thank you page or back to the home page
          window.location.href = 'index.html';
        });
      }
    });
  </script>
</body>
</html>



