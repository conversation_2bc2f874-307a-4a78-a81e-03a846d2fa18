<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CureOx Blog - Healthcare Technology Insights</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <link href="styles/theme.css" rel="stylesheet">
  <style>
    /* Blog-specific styles */
    .blog-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .blog-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    }
    .category-pill {
      transition: all 0.3s ease;
    }
    .category-pill:hover, .category-pill.active {
      background-color: var(--accent-color);
      color: white;
    }
    .article-preview-mask {
      background: linear-gradient(to bottom, transparent, var(--bg-secondary) 90%);
    }
    .featured-gradient {
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(79, 70, 229, 0.2));
    }
    .search-overlay {
      backdrop-filter: blur(8px);
      background-color: rgba(15, 23, 42, 0.9);
    }
    .author-badge {
      border: 2px solid var(--accent-color);
    }
    .blog-hero {
      background-image: url('images/blog-hero-bg.jpg');
      background-size: cover;
      background-position: center;
      position: relative;
    }
    .blog-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(15, 23, 42, 0.7), rgba(15, 23, 42, 0.9));
      z-index: 1;
    }
    .blog-hero > * {
      position: relative;
      z-index: 2;
    }
    .article-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 2rem;
    }
    .tag-cloud span {
      transition: all 0.2s ease;
    }
    .tag-cloud span:hover {
      background-color: var(--accent-color);
      color: white;
    }

    /* Animations */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    @keyframes slideInRight {
      from { transform: translateX(30px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideInLeft {
      from { transform: translateX(-30px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    .animate-fade-in {
      animation: fadeIn 0.5s ease-out forwards;
    }

    .animate-pulse {
      animation: pulse 2s infinite;
    }

    .animate-slide-right {
      animation: slideInRight 0.5s ease-out forwards;
    }

    .animate-slide-left {
      animation: slideInLeft 0.5s ease-out forwards;
    }

    .scale-102 {
      transform: scale(1.02);
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="fixed w-full z-50 bg-opacity-90 bg-dark-blue backdrop-filter backdrop-blur-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          </div>
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
              <a href="index.html#about" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">About Us</a>
              <a href="index.html#services" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Services</a>
              <a href="index.html#products" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Products</a>
              <a href="blog-redesign.html" class="px-3 py-2 rounded-md text-sm font-medium text-white bg-blue-700 transition-all">Blog</a>
            </div>
          </div>
        </div>
        <!-- Theme toggle and contact button -->
        <div class="hidden md:flex items-center space-x-4">
          <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
          <a href="index.html#contact" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Contact Us</a>
        </div>
        <div class="md:hidden">
          <button type="button" id="mobile-menu-button" class="text-gray-300 hover:text-white">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Home</a>
        <a href="index.html#about" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">About Us</a>
        <a href="index.html#services" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Services</a>
        <a href="index.html#products" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white">Products</a>
        <a href="blog-redesign.html" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-blue-700">Blog</a>
        <!-- Mobile theme toggle -->
        <div class="flex items-center justify-between px-3 py-2">
          <span class="text-base font-medium text-gray-300">Theme</span>
          <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Blog Hero Section -->
  <section class="blog-hero pt-32 pb-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 animate-slide-left">Healthcare <span class="text-gradient">Insights</span></h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-10 animate-slide-right">Expert perspectives on healthcare technology, innovation, and digital transformation.</p>

        <!-- Search Bar -->
        <div class="relative max-w-2xl mx-auto animate-fade-in" style="animation-delay: 0.3s;">
          <div class="flex items-center">
            <button id="search-button" class="absolute left-4 text-gray-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
            <input
              type="text"
              id="blog-search-input"
              placeholder="Search articles, topics, or authors..."
              class="w-full pl-12 pr-4 py-4 rounded-full bg-gray-800 bg-opacity-50 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
            >
          </div>
          <div id="blog-search-results" class="absolute z-10 mt-2 w-full bg-gray-800 rounded-lg shadow-xl hidden"></div>
        </div>
      </div>
    </div>
  </section>

  <!-- Category Navigation -->
  <section class="py-8 border-b border-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-wrap items-center justify-center gap-3" data-aos="fade-up">
        <button class="category-pill active px-5 py-2 rounded-full text-sm font-medium transition-all bg-blue-600 text-white" data-category="all">
          All Topics
        </button>
        <button class="category-pill px-5 py-2 rounded-full text-sm font-medium transition-all bg-gray-800 text-gray-300" data-category="AI">
          AI & Machine Learning
        </button>
        <button class="category-pill px-5 py-2 rounded-full text-sm font-medium transition-all bg-gray-800 text-gray-300" data-category="Telemedicine">
          Telemedicine
        </button>
        <button class="category-pill px-5 py-2 rounded-full text-sm font-medium transition-all bg-gray-800 text-gray-300" data-category="Security">
          Data Security
        </button>
        <button class="category-pill px-5 py-2 rounded-full text-sm font-medium transition-all bg-gray-800 text-gray-300" data-category="Innovation">
          Innovation
        </button>
      </div>
    </div>
  </section>

  <!-- Featured Article -->
  <section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="featured-gradient rounded-2xl overflow-hidden shadow-2xl" data-category="Innovation">
        <div class="md:flex">
          <div class="md:w-1/2 relative animate-fade-in" style="animation-delay: 0.2s;">
            <img src="images/blog-featured.jpg" alt="Featured Article" class="w-full h-full object-cover">
            <div class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full animate-pulse">FEATURED</div>
          </div>
          <div class="md:w-1/2 p-8 md:p-12 flex flex-col justify-center animate-fade-in" style="animation-delay: 0.4s;">
            <div class="flex items-center mb-4">
              <span class="bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-3 py-1 rounded-full">Innovation</span>
              <span class="text-gray-400 text-sm ml-3">July 5, 2023</span>
            </div>
            <h2 class="text-3xl md:text-4xl font-bold mb-4 leading-tight">The Digital Transformation of Healthcare: 2023 Outlook</h2>
            <p class="text-gray-300 mb-6 text-lg">A comprehensive analysis of how digital technologies are reshaping patient care, operational efficiency, and healthcare outcomes in the post-pandemic landscape.</p>
            <div class="flex items-center mb-6">
              <img src="images/author-avatar.png" alt="Author" class="w-12 h-12 rounded-full author-badge mr-4">
              <div>
                <div class="font-medium">Dr. Sarah Johnson</div>
                <div class="text-gray-400 text-sm">Healthcare Technology Specialist</div>
              </div>
            </div>
            <a href="article.html?id=featured" class="group inline-flex items-center text-blue-400 font-medium hover:text-blue-300 transition-all">
              Read Full Article
              <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Latest Articles -->
  <section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center mb-12">
        <h2 class="text-3xl font-bold" data-aos="fade-up">Latest Articles</h2>
        <div class="flex items-center">
          <span class="text-gray-400 mr-3">Sort by:</span>
          <select id="sort-articles" class="bg-gray-800 border border-gray-700 text-white rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="newest">Newest</option>
            <option value="popular">Most Popular</option>
            <option value="trending">Trending</option>
          </select>
        </div>
      </div>

      <div class="article-grid">
        <!-- Article Card 1 -->
        <div class="blog-card bg-gray-800 rounded-xl overflow-hidden shadow-lg" data-aos="fade-up" data-aos-delay="100" data-category="AI">
          <div class="relative">
            <img src="images/blog-ai-healthcare.webp" alt="AI in Healthcare" class="w-full h-56 object-cover">
            <div class="absolute top-4 right-4 bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-3 py-1 rounded-full">AI & Machine Learning</div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <img src="images/author-avatar2.png" alt="Author" class="w-8 h-8 rounded-full mr-3">
              <span class="text-sm text-gray-400">Dr. Michael Chen • June 15, 2023</span>
            </div>
            <h3 class="text-xl font-bold mb-3 line-clamp-2">The Future of AI in Healthcare Diagnostics</h3>
            <p class="text-gray-300 mb-4 line-clamp-3">Exploring how artificial intelligence is revolutionizing early disease detection and improving diagnostic accuracy across various medical specialties.</p>
            <a href="article.html?id=1" class="group inline-flex items-center text-blue-400 font-medium hover:text-blue-300 transition-all">
              Read Article
              <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>

        <!-- Article Card 2 -->
        <div class="blog-card bg-gray-800 rounded-xl overflow-hidden shadow-lg" data-aos="fade-up" data-aos-delay="200" data-category="Telemedicine">
          <div class="relative">
            <img src="images/blog-telemedicine.jpg" alt="Telemedicine Trends" class="w-full h-56 object-cover">
            <div class="absolute top-4 right-4 bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-3 py-1 rounded-full">Telemedicine</div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <img src="images/author-avatar3.jpg" alt="Author" class="w-8 h-8 rounded-full mr-3">
              <span class="text-sm text-gray-400">Jessica Martinez • May 28, 2023</span>
            </div>
            <h3 class="text-xl font-bold mb-3 line-clamp-2">5 Telemedicine Trends Reshaping Patient Care</h3>
            <p class="text-gray-300 mb-4 line-clamp-3">How virtual healthcare delivery is evolving and what medical practices need to know to stay ahead in the rapidly changing landscape.</p>
            <a href="article.html?id=2" class="group inline-flex items-center text-blue-400 font-medium hover:text-blue-300 transition-all">
              Read Article
              <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>

        <!-- Article Card 3 -->
        <div class="blog-card bg-gray-800 rounded-xl overflow-hidden shadow-lg" data-aos="fade-up" data-aos-delay="300" data-category="Security">
          <div class="relative">
            <img src="images/blog-data-security.jpg" alt="Healthcare Data Security" class="w-full h-56 object-cover">
            <div class="absolute top-4 right-4 bg-blue-600 bg-opacity-20 text-blue-400 text-xs px-3 py-1 rounded-full">Data Security</div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <img src="images/author-avatar4.png" alt="Author" class="w-8 h-8 rounded-full mr-3">
              <span class="text-sm text-gray-400">Robert Williams • April 10, 2023</span>
            </div>
            <h3 class="text-xl font-bold mb-3 line-clamp-2">Securing Patient Data: Best Practices for Healthcare Providers</h3>
            <p class="text-gray-300 mb-4 line-clamp-3">Essential cybersecurity measures every healthcare organization should implement to protect sensitive information in an increasingly digital environment.</p>
            <a href="article.html?id=3" class="group inline-flex items-center text-blue-400 font-medium hover:text-blue-300 transition-all">
              Read Article
              <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Load More Button -->
      <div class="flex justify-center mt-12">
        <button id="load-more" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-all flex items-center">
          Load More Articles
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>
    </div>
  </section>

  <!-- Topics & Tags Section -->
  <section class="py-16 bg-gray-900 bg-opacity-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold mb-10" data-aos="fade-up">Popular Topics</h2>

      <div class="tag-cloud flex flex-wrap gap-3 mb-16" data-aos="fade-up">
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Electronic Health Records</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">AI Diagnostics</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Remote Patient Monitoring</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Healthcare IT</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Cybersecurity</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Digital Transformation</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Patient Engagement</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Telehealth</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Healthcare Analytics</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Medical Devices</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Interoperability</span>
        <span class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-600 transition-all">Blockchain in Healthcare</span>
      </div>

      <!-- Featured Authors -->
      <h2 class="text-3xl font-bold mb-8" data-aos="fade-up">Featured Authors</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6" data-aos="fade-up">
        <!-- Author 1 -->
        <div class="text-center">
          <img src="images/author-avatar.png" alt="Dr. Sarah Johnson" class="w-24 h-24 rounded-full mx-auto mb-4 author-badge">
          <h3 class="font-bold">Dr. Sarah Johnson</h3>
          <p class="text-gray-400 text-sm">Healthcare Technology Specialist</p>
          <p class="text-blue-400 text-sm mt-2">12 Articles</p>
        </div>

        <!-- Author 2 -->
        <div class="text-center">
          <img src="images/author-avatar2.png" alt="Dr. Michael Chen" class="w-24 h-24 rounded-full mx-auto mb-4 author-badge">
          <h3 class="font-bold">Dr. Michael Chen</h3>
          <p class="text-gray-400 text-sm">AI Research Director</p>
          <p class="text-blue-400 text-sm mt-2">8 Articles</p>
        </div>

        <!-- Author 3 -->
        <div class="text-center">
          <img src="images/author-avatar3.jpg" alt="Jessica Martinez" class="w-24 h-24 rounded-full mx-auto mb-4 author-badge">
          <h3 class="font-bold">Jessica Martinez</h3>
          <p class="text-gray-400 text-sm">Digital Health Strategist</p>
          <p class="text-blue-400 text-sm mt-2">15 Articles</p>
        </div>

        <!-- Author 4 -->
        <div class="text-center">
          <img src="images/author-avatar4.png" alt="Robert Williams" class="w-24 h-24 rounded-full mx-auto mb-4 author-badge">
          <h3 class="font-bold">Robert Williams</h3>
          <p class="text-gray-400 text-sm">Healthcare Cybersecurity Expert</p>
          <p class="text-blue-400 text-sm mt-2">7 Articles</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Newsletter Section with Interactive Elements -->
  <section class="py-16 relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-900 to-indigo-900 opacity-50"></div>
    <div class="absolute inset-0 bg-[url('images/pattern-dots.png')] opacity-10"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="bg-gray-800 bg-opacity-80 backdrop-filter backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-2xl" data-aos="fade-up">
        <div class="md:flex items-center">
          <div class="md:w-1/2 mb-8 md:mb-0 md:pr-12">
            <h2 class="text-3xl font-bold mb-4">Join Our Newsletter</h2>
            <p class="text-xl text-gray-300 mb-6">Get personalized insights on healthcare technology delivered to your inbox.</p>

            <div class="flex items-center mb-6">
              <div class="flex -space-x-2 mr-4">
                <img src="images/author-avatar.png" alt="" class="w-10 h-10 rounded-full border-2 border-gray-800">
                <img src="images/author-avatar2.png" alt="" class="w-10 h-10 rounded-full border-2 border-gray-800">
                <img src="images/author-avatar3.jpg" alt="" class="w-10 h-10 rounded-full border-2 border-gray-800">
              </div>
              <span class="text-sm text-gray-400">Join 2,500+ healthcare professionals</span>
            </div>

            <div class="flex flex-wrap gap-2">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Weekly Updates
              </span>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Exclusive Content
              </span>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Unsubscribe Anytime
              </span>
            </div>
          </div>

          <div class="md:w-1/2">
            <form id="newsletter-form" class="space-y-4">
              <div>
                <label for="newsletter-email" class="block text-sm font-medium text-gray-300 mb-1">Email Address</label>
                <input
                  type="email"
                  id="newsletter-email"
                  placeholder="Enter your email"
                  required
                  class="w-full px-4 py-3 rounded-md bg-gray-700 text-white border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
              </div>

              <div>
                <p class="text-sm font-medium text-gray-300 mb-2">Select your interests</p>
                <div class="grid grid-cols-2 gap-2">
                  <div class="flex items-center">
                    <input type="checkbox" id="interest-ai" value="AI" class="newsletter-interest h-4 w-4 text-blue-600 rounded border-gray-600 bg-gray-700 focus:ring-blue-500">
                    <label for="interest-ai" class="ml-2 text-sm text-gray-300">AI & Machine Learning</label>
                  </div>
                  <div class="flex items-center">
                    <input type="checkbox" id="interest-telemedicine" value="Telemedicine" class="newsletter-interest h-4 w-4 text-blue-600 rounded border-gray-600 bg-gray-700 focus:ring-blue-500">
                    <label for="interest-telemedicine" class="ml-2 text-sm text-gray-300">Telemedicine</label>
                  </div>
                  <div class="flex items-center">
                    <input type="checkbox" id="interest-security" value="Security" class="newsletter-interest h-4 w-4 text-blue-600 rounded border-gray-600 bg-gray-700 focus:ring-blue-500">
                    <label for="interest-security" class="ml-2 text-sm text-gray-300">Data Security</label>
                  </div>
                  <div class="flex items-center">
                    <input type="checkbox" id="interest-innovation" value="Innovation" class="newsletter-interest h-4 w-4 text-blue-600 rounded border-gray-600 bg-gray-700 focus:ring-blue-500">
                    <label for="interest-innovation" class="ml-2 text-sm text-gray-300">Healthcare Innovation</label>
                  </div>
                </div>
              </div>

              <button type="submit" class="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-all flex items-center justify-center">
                Subscribe to Newsletter
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                </svg>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="md:col-span-1">
          <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
          <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
          <div class="flex space-x-4 mt-6">
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.205.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.849-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.203-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-all">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="md:col-span-1">
          <h3 class="text-lg font-bold mb-4 text-white">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="index.html" class="text-gray-300 hover:text-blue-400 transition-all">Home</a></li>
            <li><a href="index.html#about" class="text-gray-300 hover:text-blue-400 transition-all">About Us</a></li>
            <li><a href="index.html#services" class="text-gray-300 hover:text-blue-400 transition-all">Services</a></li>
            <li><a href="index.html#products" class="text-gray-300 hover:text-blue-400 transition-all">Products</a></li>
            <li><a href="blog-redesign.html" class="text-gray-300 hover:text-blue-400 transition-all">Blog</a></li>
          </ul>
        </div>

        <!-- Contact -->
        <div class="md:col-span-1">
          <h3 class="text-lg font-bold mb-4 text-white">Contact Us</h3>
          <div class="space-y-4">
            <div class="flex items-start">
              <svg class="h-6 w-6 text-blue-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <p class="text-gray-300">United States</p>
            </div>
            <div class="flex items-start">
              <svg class="h-6 w-6 text-blue-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <p class="text-gray-300"><EMAIL></p>
            </div>
            <div class="flex items-start">
              <svg class="h-6 w-6 text-blue-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              <p class="text-gray-300">+359 87 760 8877</p>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2025 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Initialize AOS -->
  <script>
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: false,
      mirror: true
    });
  </script>
  <script src="script.js"></script>
  <script src="js/user-preferences.js"></script>
  <script src="js/blog-features-redesign.js"></script>