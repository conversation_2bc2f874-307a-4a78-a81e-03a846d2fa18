<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dental Clinic Management System - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="styles/theme.css" rel="stylesheet">
</head>
<body>
  <!-- Navigation -->
  <nav class="fixed w-full z-50 backdrop-filter backdrop-blur-lg bg-gray-900 bg-opacity-90 shadow-lg top-0 left-0">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient flex items-center">
              <span class="mr-2">CureOx</span>
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </a>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
            <a href="product1.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management</a>
            <a href="product2.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management</a>
            <a href="product3.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management</a>
            <a href="product4.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
            <a href="contact.html" class="ml-4 bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Contact Us</a>
          </div>
        </div>
        <div class="hidden md:flex items-center space-x-4">
          <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
        <div class="md:hidden">
          <button id="mobile-menu-button" class="text-gray-300 hover:text-white focus:outline-none">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile menu, show/hide based on menu state -->
    <div id="mobile-menu" class="md:hidden hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800">
        <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
        <a href="product1.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management</a>
        <a href="product2.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management</a>
        <a href="product3.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management</a>
        <a href="product4.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
        <a href="contact.html" class="mt-2 w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md text-base font-medium transition-all block text-center">Contact Us</a>
        <div class="flex items-center justify-between px-3 py-2">
          <span class="text-gray-300">Dark/Light Mode</span>
          <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Product Hero Section -->
  <section class="pt-32 pb-20 relative">
    <div class="absolute inset-0 z-0">
      <img src="images/Dentist.jpg" alt="DentalPro Software" class="w-full h-full object-cover">
    </div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 pt-16">
      <div class="flex flex-col items-center text-center mt-12">
        <div data-aos="fade-up">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">Dental Clinic <span class="text-gradient">Management System</span></h1>
          <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">Complete practice management solution for dental clinics with patient records, scheduling, and billing features.</p>
          <div class="flex flex-wrap space-x-0 space-y-4 sm:space-y-0 sm:space-x-4 justify-center">
            <a href="demos/dentalpro-demo.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-all flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
              Interactive Demo
            </a>
            <a href="demo.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-md font-medium transition-all">Request Full Demo</a>
            <a href="pricing.html" class="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-md font-medium transition-all shadow-md hover:shadow-lg flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Pricing
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16" data-aos="fade-up">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Key <span class="text-gradient">Features</span></h2>
        <div class="w-16 h-1 bg-blue-600 mx-auto mb-6"></div>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">Discover how our Dental Clinic Management System can transform your dental practice.</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg" data-aos="zoom-in" data-aos-delay="100">
          <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">Appointment management</h3>
          <p class="text-gray-300">A flexible system for scheduling appointments and organizing patient calendars.</p>
        </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg" data-aos="zoom-in" data-aos-delay="200">
          <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">treatment session tracking</h3>
          <p class="text-gray-300">Recording and documenting treatment plans.</p>
        </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg" data-aos="zoom-in" data-aos-delay="300">
          <div class="bg-blue-600 bg-opacity-20 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">image and report management</h3>
          <p class="text-gray-300">Uploading X-ray images and documenting cases.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16" data-aos="fade-up">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Why Choose <span class="text-gradient">Dental Clinic Management System</span></h2>
        <div class="w-16 h-1 bg-blue-600 mx-auto mb-6"></div>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">Designed specifically for dental practices to improve efficiency and patient care.</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="flex items-start space-x-4" data-aos="fade-right">
          <div class="flex-shrink-0 bg-blue-600 bg-opacity-20 p-3 rounded-full">
            <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold mb-2">Improve Patient Experience</h3>
            <p class="text-gray-300">Reduce wait times and enhance communication with streamlined appointment management.</p>
          </div>
        </div>
        <div class="flex items-start space-x-4" data-aos="fade-left">
          <div class="flex-shrink-0 bg-blue-600 bg-opacity-20 p-3 rounded-full">
            <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold mb-2">Increase Efficiency</h3>
            <p class="text-gray-300">Automate administrative tasks to free up staff time for patient care.</p>
          </div>
        </div>
        <div class="flex items-start space-x-4" data-aos="fade-right">
          <div class="flex-shrink-0 bg-blue-600 bg-opacity-20 p-3 rounded-full">
            <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold mb-2">Enhance Clinical Outcomes</h3>
            <p class="text-gray-300">Access comprehensive patient records and treatment history to provide better care.</p>
          </div>
        </div>
        <div class="flex items-start space-x-4" data-aos="fade-left">
          <div class="flex-shrink-0 bg-blue-600 bg-opacity-20 p-3 rounded-full">
            <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold mb-2">Simplify Compliance</h3>
            <p class="text-gray-300">Stay compliant with regulatory requirements through automated documentation and secure record-keeping.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Related Products Section -->
  <section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16" data-aos="fade-up">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Related <span class="text-gradient">Products</span></h2>
        <div class="w-16 h-1 bg-blue-600 mx-auto mb-6"></div>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">Explore our other healthcare software solutions</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <a href="product2.html" class="bg-gray-800 rounded-lg overflow-hidden shadow-lg transform transition-all hover:scale-105" data-aos="fade-up">
          <img src="images/Pharmacy.png" alt="PharmTrack Software" class="w-full h-48 object-cover">
          <div class="p-6">
            <h3 class="text-2xl font-bold mb-2">Pharmacy Management <span class="text-gradient">Program</span></h3>
            <p class="text-gray-300 mb-4">Inventory management and prescription tracking system for pharmacies.</p>
            <div class="flex justify-end">
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-all">See Product</button>
            </div>
          </div>
        </a>
        <a href="product3.html" class="bg-gray-800 rounded-lg overflow-hidden shadow-lg transform transition-all hover:scale-105" data-aos="fade-up" data-aos-delay="100">
          <img src="images/Doctor.jpg" alt="MedOffice Software" class="w-full h-48 object-cover">
          <div class="p-6">
            <h3 class="text-2xl font-bold mb-2">Medical Clinic Management <span class="text-gradient">Program</span></h3>
            <p class="text-gray-300 mb-4">All-in-one clinic management platform with electronic health records.</p>
            <div class="flex justify-end">
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-all">See Product</button>
            </div>
          </div>
        </a>
        <a href="product4.html" class="bg-gray-800 rounded-lg overflow-hidden shadow-lg transform transition-all hover:scale-105" data-aos="fade-up" data-aos-delay="200">
          <img src="images/HeroSection.jpg" alt="Patient-Centric Application" class="w-full h-48 object-cover">
          <div class="p-6">
            <h3 class="text-2xl font-bold mb-2">Patient-Centric <span class="text-gradient">App</span></h3>
            <p class="text-gray-300 mb-4">Empowering patients with digital tools for health management and communication.</p>
            <div class="flex justify-end">
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-all">See Product</button>
            </div>
          </div>
        </a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="script.js"></script>
  <script src="js/user-preferences.js"></script>
  <script src="js/demo-interactions.js"></script>
</body>
</html>



